/**
 * Shared DynamoDB keys and constants for the CLKK payment system
 * This file contains common keys used across multiple entities to avoid duplication
 */

// ====================
// STANDARD DYNAMODB KEYS
// ====================

export const DYNAMODB_KEYS = {
  // Primary keys
  PK: "PK",
  SK: "SK",
} as const;

// ====================
// COMMON ENTITY PREFIXES
// ====================

export const ENTITY_PREFIXES = {
  // Tenant and Organization
  TENANT_PREFIX: "TENANT#",
  ORG_PREFIX: "ORG#",
  
  // User and Customer
  USER_PREFIX: "USER#",
  CUSTOMER_PREFIX: "CUSTOMER#",
  
  // Payment entities
  PAYMENT_PREFIX: "PAYMENT#",
  VAULT_PREFIX: "VAULT#",
  
  // Provider and External
  PROVIDER_PREFIX: "PROVIDER#",
  EXTERNAL_PREFIX: "EXTERNAL#",
  
  // Status and Type
  STATUS_PREFIX: "STATUS#",
  TYPE_PREFIX: "TYPE#",
  
  // Date and Reference
  DATE_PREFIX: "DATE#",
  REFERENCE_PREFIX: "REF#",
  
  // Contact Information
  EMAIL_PREFIX: "EMAIL#",
  PHONE_PREFIX: "PHONE#",
  
  // Metadata
  METADATA_SK: "METADATA",
  NAME_PREFIX: "NAME#",
} as const;

// ====================
// COMMON FIELD NAMES
// ====================

export const COMMON_FIELDS = {
  // Core Entity Fields
  FIELD_ID: "id",
  FIELD_TENANT_ID: "tenantId",
  FIELD_ORGANIZATION_ID: "organizationId",
  FIELD_USER_ID: "userId",
  FIELD_CUSTOMER_ID: "customerId",
  FIELD_EXTERNAL_ID: "externalId",
  
  // Status and Type
  FIELD_STATUS: "status",
  FIELD_TYPE: "type",
  FIELD_PROVIDER: "provider",
  
  // Contact Information
  FIELD_EMAIL: "email",
  FIELD_PHONE: "phone",
  FIELD_FIRST_NAME: "firstName",
  FIELD_LAST_NAME: "lastName",
  FIELD_DISPLAY_NAME: "displayName",
  
  // Descriptive Fields
  FIELD_NAME: "name",
  FIELD_DESCRIPTION: "description",
  FIELD_METADATA: "metadata",
  
  // Timestamps
  FIELD_CREATED_AT: "createdAt",
  FIELD_UPDATED_AT: "updatedAt",
  FIELD_COMPLETED_AT: "completedAt",
  FIELD_FAILED_AT: "failedAt",
  FIELD_LAST_USED_AT: "lastUsedAt",
  FIELD_EXPIRES_AT: "expiresAt",
  
  // Address and Contact
  FIELD_ADDRESS: "address",
  FIELD_WEBSITE: "website",
  FIELD_LOGO_URL: "logoUrl",
  FIELD_CONTACT_EMAIL: "contactEmail",
  FIELD_CONTACT_PHONE: "contactPhone",
  FIELD_LICENSE: "licenseNumber",
} as const;

// ====================
// COMMON GSI LOOKUP KEYS
// ====================

export const LOOKUP_KEYS = {
  // Email Lookup
  EMAIL_LOOKUP_PK: "EmailLookupPK",
  EMAIL_LOOKUP_SK: "EmailLookupSK",
  
  // External ID Lookup
  EXTERNAL_ID_LOOKUP_PK: "ExternalIdLookupPK",
  EXTERNAL_ID_LOOKUP_SK: "ExternalIdLookupSK",
  
  // User Lookup  
  USER_LOOKUP_PK: "UserLookupPK",
  USER_LOOKUP_SK: "UserLookupSK",
  
  // Customer Lookup
  CUSTOMER_LOOKUP_PK: "CustomerLookupPK", 
  CUSTOMER_LOOKUP_SK: "CustomerLookupSK",
  
  // Provider Lookup
  PROVIDER_LOOKUP_PK: "ProviderLookupPK",
  PROVIDER_LOOKUP_SK: "ProviderLookupSK",
  
  // Status Lookup
  STATUS_LOOKUP_PK: "StatusLookupPK",
  STATUS_LOOKUP_SK: "StatusLookupSK",
  
  // Date Lookup
  DATE_LOOKUP_PK: "DateLookupPK",
  DATE_LOOKUP_SK: "DateLookupSK",
  
  // Reference Lookup
  REFERENCE_LOOKUP_PK: "ReferenceLookupPK",
  REFERENCE_LOOKUP_SK: "ReferenceLookupSK",
  
  // Phone Lookup
  PHONE_LOOKUP_PK: "PhoneLookupPK",
  PHONE_LOOKUP_SK: "PhoneLookupSK",
} as const;

// ====================
// PAYMENT-SPECIFIC KEYS
// ====================

export const PAYMENT_SPECIFIC_KEYS = {
  // Payment Customer Lookups
  PROVIDER_CUSTOMER_LOOKUP_PK: "ProviderCustomerLookupPK",
  PROVIDER_CUSTOMER_LOOKUP_SK: "ProviderCustomerLookupSK",
  
  // Payment Vault Lookups
  CUSTOMER_VAULT_LOOKUP_PK: "CustomerVaultLookupPK",
  CUSTOMER_VAULT_LOOKUP_SK: "CustomerVaultLookupSK",
  USER_VAULT_LOOKUP_PK: "UserVaultLookupPK", 
  USER_VAULT_LOOKUP_SK: "UserVaultLookupSK",
  PROVIDER_VAULT_LOOKUP_PK: "ProviderVaultLookupPK",
  PROVIDER_VAULT_LOOKUP_SK: "ProviderVaultLookupSK",
} as const;

// ====================
// CONVENIENCE EXPORTS
// ====================

// Export all keys in a single object for easy importing
export const SHARED_KEYS = {
  ...DYNAMODB_KEYS,
  ...ENTITY_PREFIXES, 
  ...COMMON_FIELDS,
  ...LOOKUP_KEYS,
  ...PAYMENT_SPECIFIC_KEYS,
} as const;

// Export type for TypeScript intellisense
export type SharedKeyType = keyof typeof SHARED_KEYS; 
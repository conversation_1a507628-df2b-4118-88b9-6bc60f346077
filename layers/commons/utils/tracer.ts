/**
 * Tracer utility for AWS Lambda functions
 * Provides a reusable wrapper around AWS Lambda Powertools tracer
 */

import { Tracer } from "@aws-lambda-powertools/tracer";

/**
 * Create a tracer instance with consistent configuration
 * @param serviceName - Name of the service for tracing
 * @returns Configured Tracer instance
 */
export const createTracer = (serviceName: string): Tracer => {
  return new Tracer({
    serviceName,
  });
};

/**
 * Default tracer instance for general use
 * Uses environment variables for configuration
 */
export const tracer = new Tracer({
  serviceName: process.env.POWERTOOLS_SERVICE_NAME || "clkk-service",
});

/**
 * Helper function to create a custom segment
 * @param segmentName - Name of the segment
 * @param fn - Function to execute within the segment
 * @param metadata - Optional metadata to add to the segment
 * @returns Result of the function execution
 */
export const withSegment = async <T>(
  segmentName: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> => {
  const segment = tracer.getSegment();
  const subsegment = segment?.addNewSubsegment(segmentName);
  
  if (subsegment && metadata) {
    subsegment.addMetadata("custom", metadata);
  }
  
  try {
    const result = await fn();
    subsegment?.close();
    return result;
  } catch (error) {
    if (subsegment) {
      subsegment.addError(error as Error);
      subsegment.close();
    }
    throw error;
  }
};

/**
 * Helper function to add annotation to current segment
 * @param key - Annotation key
 * @param value - Annotation value
 */
export const addAnnotation = (key: string, value: string | number | boolean): void => {
  const segment = tracer.getSegment();
  if (segment) {
    segment.addAnnotation(key, value);
  }
};

/**
 * Helper function to add metadata to current segment
 * @param namespace - Metadata namespace
 * @param metadata - Metadata object
 */
export const addMetadata = (namespace: string, metadata: Record<string, any>): void => {
  const segment = tracer.getSegment();
  if (segment) {
    segment.addMetadata(namespace, metadata);
  }
};

/**
 * Helper function to add error to current segment
 * @param error - Error to add
 */
export const addError = (error: Error): void => {
  const segment = tracer.getSegment();
  if (segment) {
    segment.addError(error);
  }
};

/**
 * Common annotation keys used across the application
 */
export const ANNOTATION_KEYS = {
  // Service identifiers
  SERVICE: "service",
  OPERATION: "operation",
  
  // User context
  USER_ID: "userId",
  ORGANIZATION_ID: "organizationId",
  
  // Request context
  REQUEST_ID: "requestId",
  API_PATH: "apiPath",
  HTTP_METHOD: "httpMethod",
  
  // Business context
  PAYMENT_ID: "paymentId",
  APPLICATION_ID: "applicationId",
  WEBHOOK_TYPE: "webhookType",
  
  // Status indicators
  SUCCESS: "success",
  ERROR_CODE: "errorCode",
} as const;

/**
 * Common metadata namespaces used across the application
 */
export const METADATA_NAMESPACES = {
  REQUEST: "request",
  RESPONSE: "response",
  USER: "user",
  PAYMENT: "payment",
  APPLICATION: "application",
  WEBHOOK: "webhook",
  DATABASE: "database",
  EXTERNAL_API: "externalApi",
} as const; 
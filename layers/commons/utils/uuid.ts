/**
 * UUID Utility Service
 * 
 * Provides UUID generation with support for:
 * - UUIDv7: Time-sortable UUIDs with 1ms precision (recommended for database IDs)
 * - UUIDv4: Traditional random UUIDs
 * - Utility functions for UUID operations
 * 
 * UUIDv7 structure (RFC 9652):
 * - 48 bits: Unix timestamp in milliseconds
 * - 4 bits: Version (7)
 * - 12 bits: Random data
 * - 2 bits: Variant (10)
 * - 62 bits: Random data
 */

import crypto from 'crypto';

/**
 * Generate a UUIDv7 - time-sortable UUID with millisecond precision
 * Perfect for database primary keys as they maintain chronological order
 * 
 * @returns UUIDv7 string in standard format (e.g., 01234567-89ab-7def-8123-456789abcdef)
 */
export function generateUUIDv7(): string {
  // Generate 16 random bytes
  const bytes = crypto.randomBytes(16);
  
  // Get current timestamp in milliseconds
  const timestamp = BigInt(Date.now());
  
  // Set timestamp (48 bits, 6 bytes)
  bytes[0] = Number((timestamp >> 40n) & 0xffn);
  bytes[1] = Number((timestamp >> 32n) & 0xffn);
  bytes[2] = Number((timestamp >> 24n) & 0xffn);
  bytes[3] = Number((timestamp >> 16n) & 0xffn);
  bytes[4] = Number((timestamp >> 8n) & 0xffn);
  bytes[5] = Number(timestamp & 0xffn);
  
  // Set version (4 bits) - UUIDv7
  bytes[6] = (bytes[6] & 0x0f) | 0x70;
  
  // Set variant (2 bits) - RFC 4122 variant
  bytes[8] = (bytes[8] & 0x3f) | 0x80;
  
  return formatUUIDBytes(bytes);
}

/**
 * Generate a UUIDv4 - traditional random UUID
 * Use this when you don't need time-sortable properties
 * 
 * @returns UUIDv4 string in standard format
 */
export function generateUUIDv4(): string {
  return crypto.randomUUID();
}

/**
 * Generate a time-prefixed ID suitable for payment references
 * Format: PREFIX_TIMESTAMP_SHORTID
 * 
 * @param prefix - Prefix for the ID (e.g., 'PAY', 'TXN')
 * @param shortLength - Length of the short random suffix (default: 8)
 * @returns Formatted ID string
 */
export function generateTimeBasedId(prefix: string, shortLength: number = 8): string {
  const timestamp = Date.now();
  const shortId = generateUUIDv4().substring(0, shortLength);
  return `${prefix}_${timestamp}_${shortId}`;
}

/**
 * Extract timestamp from UUIDv7
 * 
 * @param uuidv7 - UUIDv7 string
 * @returns Date object representing when the UUID was created, or null if invalid
 */
export function extractTimestampFromUUIDv7(uuidv7: string): Date | null {
  try {
    // Remove hyphens and validate format
    const hex = uuidv7.replace(/-/g, '');
    if (hex.length !== 32) return null;
    
    // Check if it's actually a UUIDv7 (version field should be 7)
    const version = parseInt(hex.substring(12, 13), 16);
    if (version !== 7) return null;
    
    // Extract timestamp from first 12 hex characters (48 bits)
    const timestampHex = hex.substring(0, 12);
    const timestamp = parseInt(timestampHex, 16);
    
    return new Date(timestamp);
  } catch (error) {
    return null;
  }
}

/**
 * Check if a UUID string is a valid UUIDv7
 * 
 * @param uuid - UUID string to validate
 * @returns true if valid UUIDv7, false otherwise
 */
export function isUUIDv7(uuid: string): boolean {
  const timestamp = extractTimestampFromUUIDv7(uuid);
  return timestamp !== null;
}

/**
 * Check if a UUID string is a valid UUIDv4
 * 
 * @param uuid - UUID string to validate
 * @returns true if valid UUIDv4, false otherwise
 */
export function isUUIDv4(uuid: string): boolean {
  const uuidv4Regex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidv4Regex.test(uuid);
}

/**
 * Check if a string is a valid UUID (any version)
 * 
 * @param uuid - UUID string to validate
 * @returns true if valid UUID, false otherwise
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Format UUID bytes array into standard UUID string format
 * 
 * @param bytes - 16-byte array representing UUID
 * @returns Formatted UUID string with hyphens
 */
function formatUUIDBytes(bytes: Buffer): string {
  const hex = bytes.toString('hex').toLowerCase();
  return [
    hex.substring(0, 8),
    hex.substring(8, 12),
    hex.substring(12, 16),
    hex.substring(16, 20),
    hex.substring(20, 32)
  ].join('-');
}

/**
 * Generate a short unique identifier (8 characters)
 * Useful for display purposes or when full UUID is too long
 * 
 * @returns 8-character random string
 */
export function generateShortId(): string {
  return generateUUIDv4().substring(0, 8);
}

/**
 * UUID utility object with all functions
 * Use this for consistent UUID operations across the application
 */
export const UUID = {
  // Generation functions
  v7: generateUUIDv7,
  v4: generateUUIDv4,
  timeBased: generateTimeBasedId,
  short: generateShortId,
  
  // Validation functions
  isValid: isValidUUID,
  isV4: isUUIDv4,
  isV7: isUUIDv7,
  
  // Utility functions
  extractTimestamp: extractTimestampFromUUIDv7,
} as const;

// Default export for easy importing
export default UUID; 
/**
 * Metrics utility for AWS Lambda functions
 * Provides a reusable wrapper around AWS Lambda Powertools metrics
 */

import { Metrics, MetricUnit } from "@aws-lambda-powertools/metrics";

/**
 * Create a metrics instance with consistent configuration
 * @param serviceName - Name of the service for metrics namespace
 * @param namespace - Optional custom namespace (defaults to service name)
 * @returns Configured Metrics instance
 */
export const createMetrics = (
  serviceName: string, 
  namespace?: string
): Metrics => {
  return new Metrics({
    serviceName,
    namespace: namespace || serviceName,
  });
};

/**
 * Default metrics instance for general use
 * Uses environment variables for configuration
 */
export const metrics = new Metrics({
  serviceName: process.env.POWERTOOLS_SERVICE_NAME || "clkk-service",
  namespace: process.env.POWERTOOLS_METRICS_NAMESPACE || "CLKK",
});

/**
 * Re-export MetricUnits for convenience
 */
export { MetricUnit };

/**
 * Common metric names used across the application
 */
export const METRIC_NAMES = {
  // API Metrics
  API_REQUEST: "ApiRequest",
  API_SUCCESS: "ApiSuccess", 
  API_ERROR: "ApiError",
  API_LATENCY: "ApiLatency",
  
  // Authentication Metrics
  AUTH_SUCCESS: "AuthSuccess",
  AUTH_FAILURE: "AuthFailure",
  
  // Payment Metrics
  PAYMENT_CREATED: "PaymentCreated",
  PAYMENT_SUCCESS: "PaymentSuccess",
  PAYMENT_FAILED: "PaymentFailed",
  
  // Application Metrics
  APPLICATION_SUBMITTED: "ApplicationSubmitted",
  APPLICATION_APPROVED: "ApplicationApproved",
  APPLICATION_REJECTED: "ApplicationRejected",
  
  // Webhook Metrics
  WEBHOOK_RECEIVED: "WebhookReceived",
  WEBHOOK_PROCESSED: "WebhookProcessed",
  WEBHOOK_FAILED: "WebhookFailed",
  
  // Database Metrics
  DB_QUERY: "DatabaseQuery",
  DB_SUCCESS: "DatabaseSuccess",
  DB_ERROR: "DatabaseError",
} as const;

/**
 * Helper function to add a count metric
 * @param metricName - Name of the metric
 * @param value - Value to add (defaults to 1)
 * @param unit - Metric unit (defaults to Count)
 * @param dimensions - Additional dimensions
 */
export const addMetric = (
  metricName: string,
  value: number = 1,
  unit: (typeof MetricUnit)[keyof typeof MetricUnit] = MetricUnit.Count,
  dimensions?: Record<string, string>
): void => {
  if (dimensions) {
    Object.entries(dimensions).forEach(([key, val]) => {
      metrics.addDimension(key, val);
    });
  }
  metrics.addMetric(metricName, unit, value);
};

/**
 * Helper function to add a latency metric
 * @param metricName - Name of the metric
 * @param latencyMs - Latency in milliseconds
 * @param dimensions - Additional dimensions
 */
export const addLatencyMetric = (
  metricName: string,
  latencyMs: number,
  dimensions?: Record<string, string>
): void => {
  addMetric(metricName, latencyMs, MetricUnit.Milliseconds, dimensions);
};

/**
 * Helper function to measure execution time and add latency metric
 * @param metricName - Name of the metric
 * @param fn - Function to measure
 * @param dimensions - Additional dimensions
 * @returns Result of the function execution
 */
export const measureLatency = async <T>(
  metricName: string,
  fn: () => Promise<T>,
  dimensions?: Record<string, string>
): Promise<T> => {
  const startTime = Date.now();
  try {
    const result = await fn();
    const latency = Date.now() - startTime;
    addLatencyMetric(metricName, latency, dimensions);
    return result;
  } catch (error) {
    const latency = Date.now() - startTime;
    addLatencyMetric(metricName, latency, { ...dimensions, status: "error" });
    throw error;
  }
}; 
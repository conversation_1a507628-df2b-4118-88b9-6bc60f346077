import { DynamoDB } from "aws-sdk";
import { BaseEntity } from "./base-entity"; // Assuming it can reuse some base logic
import { getClient, getDocumentClient } from "../client";
import { v4 as uuidv4 } from "uuid";
import { Logger } from "@aws-lambda-powertools/logger";
import { OrganizationType } from "./organization"; // Import shared type

export type ApplicationStatus = "PENDING_APPROVAL" | "APPROVED" | "REJECTED";

// Define the structure for the submitted form data
// This should match the actual form fields collected
export interface SubmittedOrgFormData {
  companyName?: string;
  name?: string; // Add name as an alternative to companyName
  orgTypeSubmitted: OrganizationType;
  licenseNumber?: string;
  licenseExpiry?: string;
  contactInfo?: {
    address?: string;
    phone?: string;
    website?: string;
    email?: string; // Add email to contactInfo for flexibility
  };
  email?: string; // Add top-level email for direct access
  phone?: string; // Add top-level phone for direct access
  // Add other fields collected during application
}

// Constants for OrganizationApplication
export const APP_KEYS = {
  PK_PREFIX: "APP#",
  SK_METADATA: "METADATA",
  TYPE: "OrganizationApplication",
  FIELD_ID: "id",
  FIELD_TYPE: "type",
  FIELD_STATUS: "status",
  FIELD_ORG_TYPE_SUBMITTED: "orgTypeSubmitted",
  FIELD_APPLICANT_CLERK_ID: "applicantClerkUserId",
  FIELD_SUBMITTED_DATA: "submittedFormData",
  FIELD_FINAL_ORG_ID: "finalOrgId",
  FIELD_TENANT_ID: "tenantId", // Keep tenantId for consistency, might be null initially
  FIELD_CREATED_AT: "createdAt",
  FIELD_UPDATED_AT: "updatedAt",
  // Base PK/SK attribute names
  PK: "PK",
  SK: "SK",

  // Status Index attributes - for querying by application status and type
  APPLICATION_STATUS_KEY: "applicationStatus", // GSI1PK - Partition key for status index
  APPLICATION_TYPE_ID_KEY: "applicationTypeId", // GSI1SK - Sort key for status index
  STATUS_PENDING: "PENDING_APPROVAL", // Status value constant
  APP_TYPE_PREFIX: "APP_TYPE#", // Prefix for app type in sort key
  INDEX_APPLICATION_STATUS: "applicationStatusIndex", // The GSI name for status

  // Email Index attributes - for querying by email
  APPLICATION_EMAIL_KEY: "applicationEmail", // GSI2PK - Partition key for email index
  APPLICATION_EMAIL_STATUS_KEY: "applicationEmailStatus", // GSI2SK - Sort key for email index
  INDEX_APPLICATION_EMAIL: "applicationEmailIndex", // The GSI name for email
  EMAIL_PREFIX: "EMAIL#", // Prefix for email in partition key

  // Phone Index attributes - for querying by phone
  APPLICATION_PHONE_KEY: "applicationPhone", // GSI3PK - Partition key for phone index
  APPLICATION_PHONE_STATUS_KEY: "applicationPhoneStatus", // GSI3SK - Sort key for phone index
  INDEX_APPLICATION_PHONE: "applicationPhoneIndex", // The GSI name for phone
  PHONE_PREFIX: "PHONE#", // Prefix for phone in partition key
};

const logger = new Logger({ serviceName: "organization-application-service" });
const getTableName = (): string => process.env.TABLE_NAME || "";

/**
 * Represents an application to create an Organization.
 * Stored with PK: APP#<app_uuid>
 */
export class OrganizationApplication extends BaseEntity {
  id: string;
  orgTypeSubmitted: OrganizationType;
  status: ApplicationStatus;
  applicantClerkUserId: string;
  submittedFormData: SubmittedOrgFormData;
  finalOrgId?: string; // Populated upon approval
  // tenantId is inherited from BaseEntity as string

  constructor(
    applicantClerkUserId: string,
    submittedFormData: SubmittedOrgFormData,
    id?: string, // Optional: Generate if not provided
    status: ApplicationStatus = "PENDING_APPROVAL",
    tenantId: string | null = null // Allow null tenantId for global applications conceptually
  ) {
    // Use a placeholder for tenantId in BaseEntity if apps are global initially
    // BaseEntity's constructor will set this.tenantId to (tenantId || "APP_GLOBAL")
    super(tenantId || "APP_GLOBAL");
    this.id = id || uuidv4();
    this.orgTypeSubmitted = submittedFormData.orgTypeSubmitted;
    this.status = status;
    this.applicantClerkUserId = applicantClerkUserId;
    this.submittedFormData = submittedFormData;
    // this.tenantId is now handled by BaseEntity and will be a string
  }

  get entityType(): string {
    return APP_KEYS.TYPE;
  }

  get pk(): string {
    return `${APP_KEYS.PK_PREFIX}${this.id}`;
  }

  get sk(): string {
    return APP_KEYS.SK_METADATA;
  }

  /**
   * Extract email from submitted form data
   * Email could be at the top level or in contactInfo
   */
  get email(): string | undefined {
    return (
      this.submittedFormData.email || this.submittedFormData.contactInfo?.email
    );
  }

  /**
   * Extract phone from submitted form data
   * Phone could be at the top level or in contactInfo
   */
  get phone(): string | undefined {
    return (
      this.submittedFormData.phone || this.submittedFormData.contactInfo?.phone
    );
  }

  /**
   * Normalize a phone number by removing non-numeric characters
   * @param phone Phone number to normalize
   * @returns Normalized phone number
   */
  static normalizePhone(phone?: string): string | undefined {
    if (!phone) return undefined;
    return phone.replace(/\D/g, "");
  }

  /**
   * Normalize an email address by converting to lowercase
   * @param email Email address to normalize
   * @returns Normalized email address
   */
  static normalizeEmail(email?: string): string | undefined {
    if (!email) return undefined;
    return email.toLowerCase();
  }

  toItem(): DynamoDB.AttributeMap {
    const item: DynamoDB.AttributeMap = {
      PK: { S: this.pk },
      SK: { S: this.sk },
      ...this.baseFields(), // Include createdAt, updatedAt
      [APP_KEYS.FIELD_ID]: { S: this.id },
      [APP_KEYS.FIELD_TYPE]: { S: this.entityType }, // Store the specific type
      [APP_KEYS.FIELD_ORG_TYPE_SUBMITTED]: { S: this.orgTypeSubmitted },
      [APP_KEYS.FIELD_STATUS]: { S: this.status },
      [APP_KEYS.FIELD_APPLICANT_CLERK_ID]: { S: this.applicantClerkUserId },
      [APP_KEYS.FIELD_SUBMITTED_DATA]: {
        M: this.marshallObject(this.submittedFormData),
      },
      ...(this.finalOrgId && {
        [APP_KEYS.FIELD_FINAL_ORG_ID]: { S: this.finalOrgId },
      }),
      // Status index attributes
      [APP_KEYS.APPLICATION_STATUS_KEY]: { S: this.status }, // Use application status as partition key
      [APP_KEYS.APPLICATION_TYPE_ID_KEY]: {
        S: `${APP_KEYS.APP_TYPE_PREFIX}${this.orgTypeSubmitted}#${this.id}`, // Combine type and ID for sort key
      },
    };

    // Add email GSI if email is available
    const email = OrganizationApplication.normalizeEmail(this.email);
    if (email) {
      item[APP_KEYS.APPLICATION_EMAIL_KEY] = {
        S: `${APP_KEYS.EMAIL_PREFIX}${email}`,
      };
      item[APP_KEYS.APPLICATION_EMAIL_STATUS_KEY] = { S: this.status };
    }

    // Add phone GSI if phone is available
    const phone = OrganizationApplication.normalizePhone(this.phone);
    if (phone) {
      item[APP_KEYS.APPLICATION_PHONE_KEY] = {
        S: `${APP_KEYS.PHONE_PREFIX}${phone}`,
      };
      item[APP_KEYS.APPLICATION_PHONE_STATUS_KEY] = { S: this.status };
    }

    // Adjust tenantId handling if it was set to placeholder
    if (this.tenantId === "APP_GLOBAL") {
      // Decide whether to store null, omit, or keep placeholder
      // Omitting might be cleanest if tenantId is truly optional for applications
      delete item[APP_KEYS.FIELD_TENANT_ID];
    } else if (this.tenantId) {
      item[APP_KEYS.FIELD_TENANT_ID] = { S: this.tenantId };
    }

    return item;
  }

  toDocClientItem(): Record<string, any> {
    const item: Record<string, any> = {
      PK: this.pk,
      SK: this.sk,
      ...this.baseDocClientFields(), // Include createdAt, updatedAt
      [APP_KEYS.FIELD_ID]: this.id,
      [APP_KEYS.FIELD_TYPE]: this.entityType,
      [APP_KEYS.FIELD_ORG_TYPE_SUBMITTED]: this.orgTypeSubmitted,
      [APP_KEYS.FIELD_STATUS]: this.status,
      [APP_KEYS.FIELD_APPLICANT_CLERK_ID]: this.applicantClerkUserId,
      [APP_KEYS.FIELD_SUBMITTED_DATA]: this.submittedFormData,
      // Status index attributes
      [APP_KEYS.APPLICATION_STATUS_KEY]: this.status,
      [APP_KEYS.APPLICATION_TYPE_ID_KEY]: `${APP_KEYS.APP_TYPE_PREFIX}${this.orgTypeSubmitted}#${this.id}`,
    };

    if (this.finalOrgId) {
      item[APP_KEYS.FIELD_FINAL_ORG_ID] = this.finalOrgId;
    }

    // Add email GSI if email is available
    const email = OrganizationApplication.normalizeEmail(this.email);
    if (email) {
      item[APP_KEYS.APPLICATION_EMAIL_KEY] = `${APP_KEYS.EMAIL_PREFIX}${email}`;
      item[APP_KEYS.APPLICATION_EMAIL_STATUS_KEY] = this.status;
    }

    // Add phone GSI if phone is available
    const phone = OrganizationApplication.normalizePhone(this.phone);
    if (phone) {
      item[APP_KEYS.APPLICATION_PHONE_KEY] = `${APP_KEYS.PHONE_PREFIX}${phone}`;
      item[APP_KEYS.APPLICATION_PHONE_STATUS_KEY] = this.status;
    }

    // Adjust tenantId handling if it was set to placeholder
    if (this.tenantId === "APP_GLOBAL") {
      // Omit tenantId if it's the placeholder value
      delete item[APP_KEYS.FIELD_TENANT_ID];
    } else if (this.tenantId) {
      item[APP_KEYS.FIELD_TENANT_ID] = this.tenantId;
    }

    return item;
  }

  // Helper to marshall nested objects (like submittedFormData)
  private marshallObject(obj: Record<string, any>): DynamoDB.AttributeMap {
    const result: DynamoDB.AttributeMap = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined && value !== null) {
        if (typeof value === "string") {
          result[key] = { S: value };
        } else if (typeof value === "number") {
          result[key] = { N: value.toString() };
        } else if (typeof value === "boolean") {
          result[key] = { BOOL: value };
        } else if (typeof value === "object" && !Array.isArray(value)) {
          // Recursively marshall nested objects
          result[key] = { M: this.marshallObject(value) };
        } else if (Array.isArray(value)) {
          // Simple array marshalling (adjust as needed for complex arrays)
          result[key] = {
            L: value.map((v) =>
              typeof v === "string" ? { S: v } : { NULL: true }
            ),
          };
        }
      }
    }
    return result;
  }

  // Helper to unmarshall nested objects
  private static unmarshallObject(
    map?: DynamoDB.AttributeMap
  ): Record<string, any> | undefined {
    if (!map) return undefined;
    const result: Record<string, any> = {};
    for (const [key, value] of Object.entries(map)) {
      if (value.S) result[key] = value.S;
      else if (value.N) result[key] = parseFloat(value.N);
      else if (value.BOOL !== undefined) result[key] = value.BOOL;
      else if (value.M) result[key] = this.unmarshallObject(value.M);
      else if (value.L) result[key] = value.L.map((v) => v.S || null);
    }
    return result;
  }

  static fromItem(item?: DynamoDB.AttributeMap): OrganizationApplication {
    if (!item) throw new Error("No application item found!");

    try {
      const id = item[APP_KEYS.FIELD_ID]?.S;
      const orgTypeSubmitted = item[APP_KEYS.FIELD_ORG_TYPE_SUBMITTED]
        ?.S as OrganizationType;
      const status = item[APP_KEYS.FIELD_STATUS]?.S as ApplicationStatus;
      const applicantClerkUserId = item[APP_KEYS.FIELD_APPLICANT_CLERK_ID]?.S;
      const submittedFormDataMap = item[APP_KEYS.FIELD_SUBMITTED_DATA]?.M;
      const finalOrgId = item[APP_KEYS.FIELD_FINAL_ORG_ID]?.S;
      const tenantId = item[APP_KEYS.FIELD_TENANT_ID]?.S || null; // Handle potentially absent tenantId

      // Basic validation
      if (!id) throw new Error("Invalid item: Missing id");
      if (!orgTypeSubmitted)
        throw new Error("Invalid item: Missing orgTypeSubmitted");
      if (!status) throw new Error("Invalid item: Missing status");
      if (!applicantClerkUserId)
        throw new Error("Invalid item: Missing applicantClerkUserId");
      if (!submittedFormDataMap)
        throw new Error("Invalid item: Missing submittedFormData");

      const submittedFormData = this.unmarshallObject(submittedFormDataMap) as
        | SubmittedOrgFormData
        | undefined;
      if (!submittedFormData)
        throw new Error("Invalid item: Could not parse submittedFormData");
      // Add more specific validation for submittedFormData content if needed
      if (
        (!submittedFormData.companyName && !submittedFormData.name) ||
        !submittedFormData.orgTypeSubmitted
      ) {
        throw new Error(
          "Invalid item: submittedFormData missing required fields"
        );
      }

      const application = new OrganizationApplication(
        applicantClerkUserId,
        submittedFormData, // Already includes orgTypeSubmitted
        id,
        status,
        tenantId
      );

      // Re-hydrate optional and base fields
      application.finalOrgId = finalOrgId;
      application.createdAt =
        item[APP_KEYS.FIELD_CREATED_AT]?.S || new Date(0).toISOString(); // Provide default if missing
      application.updatedAt =
        item[APP_KEYS.FIELD_UPDATED_AT]?.S || new Date(0).toISOString(); // Provide default if missing

      return application;
    } catch (error) {
      logger.error("Error creating OrganizationApplication from item", {
        error,
        item,
      });
      throw new Error(
        `Failed to create OrganizationApplication from item: ${error instanceof Error ? error.message : error}`
      );
    }
  }

  static fromDocClientItem(
    item?: Record<string, any>
  ): OrganizationApplication {
    if (!item) throw new Error("No application item found!");

    try {
      const id = item[APP_KEYS.FIELD_ID];
      const orgTypeSubmitted = item[
        APP_KEYS.FIELD_ORG_TYPE_SUBMITTED
      ] as OrganizationType;
      const status = item[APP_KEYS.FIELD_STATUS] as ApplicationStatus;
      const applicantClerkUserId = item[APP_KEYS.FIELD_APPLICANT_CLERK_ID];
      const submittedFormData = item[
        APP_KEYS.FIELD_SUBMITTED_DATA
      ] as SubmittedOrgFormData;
      const finalOrgId = item[APP_KEYS.FIELD_FINAL_ORG_ID];
      const tenantId = item[APP_KEYS.FIELD_TENANT_ID] || null;

      // Basic validation
      if (!id) throw new Error("Invalid item: Missing id");
      if (!orgTypeSubmitted)
        throw new Error("Invalid item: Missing orgTypeSubmitted");
      if (!status) throw new Error("Invalid item: Missing status");
      if (!applicantClerkUserId)
        throw new Error("Invalid item: Missing applicantClerkUserId");
      if (!submittedFormData)
        throw new Error("Invalid item: Missing submittedFormData");
      if (
        (!submittedFormData.companyName && !submittedFormData.name) ||
        !submittedFormData.orgTypeSubmitted
      ) {
        throw new Error(
          "Invalid item: submittedFormData missing required fields"
        );
      }

      const application = new OrganizationApplication(
        applicantClerkUserId,
        submittedFormData,
        id,
        status,
        tenantId
      );

      // Re-hydrate optional and base fields
      application.finalOrgId = finalOrgId;
      application.createdAt =
        item[APP_KEYS.FIELD_CREATED_AT] || new Date(0).toISOString();
      application.updatedAt =
        item[APP_KEYS.FIELD_UPDATED_AT] || new Date(0).toISOString();

      return application;
    } catch (error) {
      logger.error("Error creating OrganizationApplication from item", {
        error,
        item,
      });
      throw new Error(
        `Failed to create OrganizationApplication from item: ${error instanceof Error ? error.message : error}`
      );
    }
  }

  /**
   * List pending organization applications by organization type.
   * Uses applicationStatusIndex: PK=PENDING_APPROVAL, SK=APP_TYPE#{orgType}#...
   * @param orgType The type of organization application to list.
   * @returns Array of pending applications.
   */
  static async listPendingByType(
    orgType: OrganizationType
  ): Promise<OrganizationApplication[]> {
    if (!orgType)
      throw new Error(
        "Organization type is required to list pending applications."
      );

    const client = getDocumentClient();
    const statusKey = APP_KEYS.STATUS_PENDING; // e.g., "PENDING_APPROVAL"
    const typeIdPrefix = `${APP_KEYS.APP_TYPE_PREFIX}${orgType}#`; // e.g., "APP_TYPE#AUTHORITY#"

    try {
      const response = await client
        .query({
          TableName: getTableName(),
          IndexName: APP_KEYS.INDEX_APPLICATION_STATUS,
          KeyConditionExpression: `${APP_KEYS.APPLICATION_STATUS_KEY} = :statusKey AND begins_with(${APP_KEYS.APPLICATION_TYPE_ID_KEY}, :typeIdPrefix)`,
          ExpressionAttributeValues: {
            ":statusKey": statusKey,
            ":typeIdPrefix": typeIdPrefix,
          },
        })
        .promise();

      return (response.Items || []).map((item) =>
        OrganizationApplication.fromDocClientItem(item)
      );
    } catch (error) {
      logger.error("Error listing pending organization applications by type", {
        error,
        orgType,
      });
      throw error;
    }
  }

  /**
   * Find applications by email
   * @param email Email address to search for
   * @param status Optional status to filter by
   * @returns Applications with the given email
   */
  static async findByEmail(
    email: string,
    status?: ApplicationStatus
  ): Promise<OrganizationApplication[]> {
    if (!email) {
      throw new Error("Email is required to find applications by email");
    }

    const normalizedEmail = this.normalizeEmail(email);
    const client = getDocumentClient();

    try {
      // Always initialize ExpressionAttributeValues as non-undefined
      let queryParams: DynamoDB.DocumentClient.QueryInput = {
        TableName: getTableName(),
        IndexName: APP_KEYS.INDEX_APPLICATION_EMAIL,
        KeyConditionExpression: `${APP_KEYS.APPLICATION_EMAIL_KEY} = :email`,
        ExpressionAttributeValues: {
          ":email": `${APP_KEYS.EMAIL_PREFIX}${normalizedEmail}`,
        },
      };

      // Add status filter if provided
      if (status) {
        queryParams = {
          ...queryParams,
          KeyConditionExpression: `${APP_KEYS.APPLICATION_EMAIL_KEY} = :email AND ${APP_KEYS.APPLICATION_EMAIL_STATUS_KEY} = :status`,
          ExpressionAttributeValues: {
            ...queryParams.ExpressionAttributeValues,
            ":status": status,
          },
        };
      }

      const response = await client.query(queryParams).promise();

      return (response.Items || []).map((item) =>
        OrganizationApplication.fromDocClientItem(item)
      );
    } catch (error) {
      logger.error("Error finding applications by email", { error, email });
      throw error;
    }
  }

  /**
   * Find applications by phone number
   * @param phone Phone number to search for
   * @param status Optional status to filter by
   * @returns Applications with the given phone number
   */
  static async findByPhone(
    phone: string,
    status?: ApplicationStatus
  ): Promise<OrganizationApplication[]> {
    if (!phone) {
      throw new Error("Phone is required to find applications by phone");
    }

    const normalizedPhone = this.normalizePhone(phone);
    const client = getDocumentClient();

    try {
      // Always initialize ExpressionAttributeValues as non-undefined
      let queryParams: DynamoDB.DocumentClient.QueryInput = {
        TableName: getTableName(),
        IndexName: APP_KEYS.INDEX_APPLICATION_PHONE,
        KeyConditionExpression: `${APP_KEYS.APPLICATION_PHONE_KEY} = :phone`,
        ExpressionAttributeValues: {
          ":phone": `${APP_KEYS.PHONE_PREFIX}${normalizedPhone}`,
        },
      };

      // Add status filter if provided
      if (status) {
        queryParams = {
          ...queryParams,
          KeyConditionExpression: `${APP_KEYS.APPLICATION_PHONE_KEY} = :phone AND ${APP_KEYS.APPLICATION_PHONE_STATUS_KEY} = :status`,
          ExpressionAttributeValues: {
            ...queryParams.ExpressionAttributeValues,
            ":status": status,
          },
        };
      }

      const response = await client.query(queryParams).promise();

      return (response.Items || []).map((item) =>
        OrganizationApplication.fromDocClientItem(item)
      );
    } catch (error) {
      logger.error("Error finding applications by phone", { error, phone });
      throw error;
    }
  }

  /**
   * Check if an application with the given email or phone already exists
   * @param email Email to check
   * @param phone Phone to check
   * @param status Optional status to filter by (e.g., only check PENDING_APPROVAL)
   * @returns Object indicating if application exists and which field caused the conflict
   */
  static async checkExistingApplication(
    email?: string,
    phone?: string,
    status?: ApplicationStatus
  ): Promise<{
    exists: boolean;
    field?: "email" | "phone";
    application?: OrganizationApplication;
  }> {
    // Check by email first if provided
    if (email) {
      const emailApps = await this.findByEmail(email, status);
      if (emailApps.length > 0) {
        return {
          exists: true,
          field: "email",
          application: emailApps[0],
        };
      }
    }

    // Then check by phone if provided
    if (phone) {
      const phoneApps = await this.findByPhone(phone, status);
      if (phoneApps.length > 0) {
        return {
          exists: true,
          field: "phone",
          application: phoneApps[0],
        };
      }
    }

    // No existing applications found
    return { exists: false };
  }

  /**
   * Verify uniqueness constraints before creating an application
   * @param application The application to validate
   * @throws Error if application with the same email or phone exists
   */
  static async verifyUniqueness(
    application: OrganizationApplication
  ): Promise<void> {
    const email = application.email;
    const phone = application.phone;

    // Skip if neither email nor phone is provided
    if (!email && !phone) {
      logger.warn("No email or phone provided for uniqueness check", {
        applicationId: application.id,
      });
      return;
    }

    // Check for existing applications with the same email or phone
    const result = await this.checkExistingApplication(
      email,
      phone,
      "PENDING_APPROVAL" // Only check against pending applications
    );

    if (result.exists) {
      const fieldMessage =
        result.field === "email"
          ? `Email "${email}" is already in use`
          : `Phone number "${phone}" is already in use`;

      logger.warn("Duplicate application attempted", {
        applicationId: application.id,
        existingApplicationId: result.application?.id,
        field: result.field,
      });

      throw new Error(
        `An application with this ${result.field} already exists. ${fieldMessage}`
      );
    }
  }

  // Convenience method to update status and add finalOrgId upon approval
  async approve(finalOrgId: string, tenantId?: string): Promise<this> {
    if (this.status !== "PENDING_APPROVAL") {
      throw new Error(
        `Application ${this.id} is not in PENDING_APPROVAL state.`
      );
    }
    this.status = "APPROVED";
    this.finalOrgId = finalOrgId;
    if (tenantId && this.tenantId === "APP_GLOBAL") {
      // Assign tenantId if it wasn't set initially
      this.tenantId = tenantId;
    } else if (tenantId && this.tenantId !== tenantId) {
      // Handle potential tenant mismatch if needed
      logger.warn(
        "Approving application with a different tenantId than initially potentially set",
        {
          appId: this.id,
          initialTenantId: this.tenantId,
          approvalTenantId: tenantId,
        }
      );
      this.tenantId = tenantId;
    }
    this.updateTimestamp(); // Update updatedAt from BaseEntity
    await this.update(); // Persist changes using BaseEntity.update()
    return this;
  }

  // Convenience method to reject an application
  async reject(): Promise<this> {
    if (this.status !== "PENDING_APPROVAL") {
      throw new Error(
        `Application ${this.id} is not in PENDING_APPROVAL state.`
      );
    }
    this.status = "REJECTED";
    this.updateTimestamp();
    await this.update();
    return this;
  }

  /**
   * Override the create method to verify uniqueness before creating
   */
  async create(): Promise<this> {
    // Verify that no application with the same email or phone exists
    await OrganizationApplication.verifyUniqueness(this);

    // Call the parent's create method (from BaseEntity)
    return super.create();
  }
}

// --- Convenience Functions --- (Optional)

export const createOrgApplication = async (
  application: OrganizationApplication
): Promise<OrganizationApplication> => {
  // This will call the overridden create method which includes uniqueness check
  return application.create();
};

export const getOrgApplicationById = async (
  applicationId: string
): Promise<OrganizationApplication> => {
  const client = getDocumentClient();
  try {
    const response = await client
      .get({
        TableName: getTableName(),
        Key: {
          [APP_KEYS.PK]: `${APP_KEYS.PK_PREFIX}${applicationId}`,
          [APP_KEYS.SK]: APP_KEYS.SK_METADATA,
        },
      })
      .promise();

    if (!response.Item) {
      throw new Error(`Application not found: ${applicationId}`);
    }

    return OrganizationApplication.fromDocClientItem(response.Item);
  } catch (error) {
    logger.error("Error getting application by ID", { error, applicationId });
    throw error;
  }
};

/**
 * Find applications by email
 * @param email Email to search for
 * @param status Optional status to filter by
 */
export const findApplicationsByEmail = async (
  email: string,
  status?: ApplicationStatus
): Promise<OrganizationApplication[]> => {
  return OrganizationApplication.findByEmail(email, status);
};

/**
 * Find applications by phone
 * @param phone Phone number to search for
 * @param status Optional status to filter by
 */
export const findApplicationsByPhone = async (
  phone: string,
  status?: ApplicationStatus
): Promise<OrganizationApplication[]> => {
  return OrganizationApplication.findByPhone(phone, status);
};

/**
 * Check if an application with the same email or phone exists
 * @param email Email to check
 * @param phone Phone to check
 * @param status Optional application status to filter by
 */
export const checkDuplicateApplication = async (
  email?: string,
  phone?: string,
  status?: ApplicationStatus
): Promise<{
  exists: boolean;
  field?: "email" | "phone";
  application?: OrganizationApplication;
}> => {
  return OrganizationApplication.checkExistingApplication(email, phone, status);
};

import {
  Organization,
  ORGANIZATION_KEYS,
  OrganizationStatus,
  OrganizationType,
  createOrganization,
  getOrganization,
  listOrganizations,
  getOrganizationByLicense,
} from "../organization";
import { BaseEntity } from "../base-entity";
import { getClient } from "../../client"; // Adjusted path

// Mock the DynamoDB client
const mockGetItemPromise = jest.fn();
const mockQueryPromise = jest.fn();
const mockScanPromise = jest.fn();
const mockPutItemPromise = jest.fn();

const mockGetItem = jest.fn(() => ({ promise: mockGetItemPromise }));
const mockQuery = jest.fn(() => ({ promise: mockQueryPromise }));
const mockScan = jest.fn(() => ({ promise: mockScanPromise }));
const mockPutItem = jest.fn(() => ({ promise: mockPutItemPromise }));

jest.mock("../../client", () => ({
  getClient: jest.fn(() => ({
    getItem: mockGetItem,
    query: mockQuery,
    scan: mockScan,
    putItem: mockPutItem,
  })),
}));

// Mock v4 as uuidv4
const mockUuidv4 = jest.fn();
jest.mock("uuid", () => ({
  v4: () => mockUuidv4(),
}));

describe("Organization Entity", () => {
  const tenantId = "test-tenant";
  const orgId = "org-123";
  const orgName = "Test Organization";
  const orgType: OrganizationType = "SERVICE_PROVIDER";
  const orgStatus: OrganizationStatus = "ACTIVE";
  const licenseNumber = "LIC-789";

  const baseOrgData = {
    tenantId,
    id: orgId,
    name: orgName,
    type: orgType,
    status: orgStatus,
  };

  const fullOrgData = {
    ...baseOrgData,
    description: "A test organization",
    address: "123 Test St",
    contactEmail: "<EMAIL>",
    contactPhone: "555-1234",
    website: "https://testorg.com",
    logoUrl: "https://testorg.com/logo.png",
    licenseNumber,
  };

  let testOrg: Organization;

  beforeEach(() => {
    // Reset mocks before each test
    mockGetItemPromise.mockReset();
    mockQueryPromise.mockReset();
    mockScanPromise.mockReset();
    mockPutItemPromise.mockReset();
    mockGetItem.mockClear();
    mockQuery.mockClear();
    mockScan.mockClear();
    mockPutItem.mockClear();
    (getClient as jest.Mock).mockClear();
    mockUuidv4.mockReturnValue("mock-uuid-1234"); // Default mock UUID

    // Set environment variable for table name
    process.env.TABLE_NAME = "test-table";
  });

  afterEach(() => {
    // Clean up environment variable
    delete process.env.TABLE_NAME;
  });

  describe("Constructor", () => {
    it("should create an instance with all properties", () => {
      const org = new Organization(
        tenantId,
        orgId,
        orgName,
        orgType,
        orgStatus,
        {
          description: "Test Description",
          address: "123 Main St",
          contactEmail: "<EMAIL>",
          contactPhone: "************",
          website: "http://example.com",
          logoUrl: "http://example.com/logo.png",
          licenseNumber: "LIC123",
        }
      );
      expect(org.id).toBe(orgId);
      expect(org.name).toBe(orgName);
      expect(org.type).toBe(orgType);
      expect(org.status).toBe(orgStatus);
      expect(org.description).toBe("Test Description");
      expect(org.licenseNumber).toBe("LIC123");
      expect(org.tenantId).toBe(tenantId);
      expect(org.createdAt).toBeDefined();
      expect(org.updatedAt).toBeDefined();
      expect(org.createdAt).toEqual(org.updatedAt);
    });

    it("should create an instance with minimal properties and default status", () => {
      const org = new Organization(tenantId, orgId, orgName, orgType);
      expect(org.status).toBe("ACTIVE"); // Default status
      expect(org.description).toBeUndefined();
      expect(org.licenseNumber).toBeUndefined();
    });

    it("should generate an id if not provided (empty string)", () => {
      mockUuidv4.mockReturnValueOnce("generated-uuid");
      const org = new Organization(tenantId, "", orgName, orgType); // Pass empty string for id
      expect(org.id).toBe("generated-uuid");
    });

    it("should generate an id if null is provided", () => {
      mockUuidv4.mockReturnValueOnce("generated-uuid-for-null");
      // @ts-expect-error testing with null for id, though type expects string
      const org = new Organization(tenantId, null, orgName, orgType);
      expect(org.id).toBe("generated-uuid-for-null");
    });

    it("should use provided id if not empty", () => {
      const specificId = "specific-org-id";
      const org = new Organization(tenantId, specificId, orgName, orgType);
      expect(org.id).toBe(specificId);
    });
  });

  describe("Getters", () => {
    beforeEach(() => {
      testOrg = new Organization(tenantId, orgId, orgName, orgType);
    });

    it('entityType should return "ORGANIZATION"', () => {
      expect(testOrg.entityType).toBe("ORGANIZATION");
    });

    it("pk should return correct primary key for Access Pattern #6", () => {
      expect(testOrg.pk).toBe(`${ORGANIZATION_KEYS.TENANT_PREFIX}${tenantId}`);
    });

    it("sk should return correct sort key for Access Pattern #6", () => {
      expect(testOrg.sk).toBe(`${ORGANIZATION_KEYS.ORG_PREFIX}${orgId}`);
    });
  });

  describe("toItem()", () => {
    it("should serialize with all properties including GSIs", () => {
      const org = new Organization(
        fullOrgData.tenantId,
        fullOrgData.id,
        fullOrgData.name,
        fullOrgData.type,
        fullOrgData.status,
        fullOrgData // options object
      );
      const item = org.toItem();

      // Base Keys from Item.keys()
      expect(item.PK).toEqual({
        S: `${ORGANIZATION_KEYS.TENANT_PREFIX}${fullOrgData.tenantId}`,
      });
      expect(item.SK).toEqual({
        S: `${ORGANIZATION_KEYS.ORG_PREFIX}${fullOrgData.id}`,
      });

      // Base Fields from BaseEntity.baseFields()
      expect(item.tenantId).toEqual({ S: fullOrgData.tenantId });
      expect(item.createdAt).toBeDefined();
      expect(item.updatedAt).toBeDefined();

      // Org Fields
      expect(item[ORGANIZATION_KEYS.FIELD_ID]).toEqual({ S: fullOrgData.id });
      expect(item[ORGANIZATION_KEYS.FIELD_NAME]).toEqual({
        S: fullOrgData.name,
      });
      expect(item[ORGANIZATION_KEYS.FIELD_TYPE]).toEqual({
        S: fullOrgData.type,
      });
      expect(item[ORGANIZATION_KEYS.FIELD_STATUS]).toEqual({
        S: fullOrgData.status,
      });
      expect(item[ORGANIZATION_KEYS.FIELD_DESCRIPTION]).toEqual({
        S: fullOrgData.description,
      });
      expect(item[ORGANIZATION_KEYS.FIELD_ADDRESS]).toEqual({
        S: fullOrgData.address,
      });
      expect(item[ORGANIZATION_KEYS.FIELD_CONTACT_EMAIL]).toEqual({
        S: fullOrgData.contactEmail,
      });
      expect(item[ORGANIZATION_KEYS.FIELD_CONTACT_PHONE]).toEqual({
        S: fullOrgData.contactPhone,
      });
      expect(item[ORGANIZATION_KEYS.FIELD_WEBSITE]).toEqual({
        S: fullOrgData.website,
      });
      expect(item[ORGANIZATION_KEYS.FIELD_LOGO_URL]).toEqual({
        S: fullOrgData.logoUrl,
      });
      expect(item[ORGANIZATION_KEYS.FIELD_LICENSE]).toEqual({
        S: fullOrgData.licenseNumber,
      });

      // GSI2 for Access Pattern #7 (List by type)
      expect(item[ORGANIZATION_KEYS.GSI2PK]).toEqual({
        S: `${ORGANIZATION_KEYS.TENANT_PREFIX}${fullOrgData.tenantId}`,
      });
      expect(item[ORGANIZATION_KEYS.GSI2SK]).toEqual({
        S: `${ORGANIZATION_KEYS.TYPE_PREFIX}${fullOrgData.type}#${ORGANIZATION_KEYS.ORG_PREFIX}${fullOrgData.id}`,
      });

      // GSI1 for Access Pattern #9 (Get by license)
      expect(item[ORGANIZATION_KEYS.GSI1PK]).toEqual({
        S: `LICENSE#${fullOrgData.licenseNumber}`,
      });
      expect(item[ORGANIZATION_KEYS.GSI1SK]).toEqual({
        S: `${ORGANIZATION_KEYS.TENANT_PREFIX}${fullOrgData.tenantId}`,
      });
    });

    it("should serialize with minimal properties and omit license-related GSIs if licenseNumber is absent", () => {
      const org = new Organization(tenantId, orgId, orgName, orgType);
      const item = org.toItem();

      expect(item[ORGANIZATION_KEYS.FIELD_ID]).toEqual({ S: orgId });
      expect(item[ORGANIZATION_KEYS.FIELD_LICENSE]).toBeUndefined();
      expect(item[ORGANIZATION_KEYS.GSI1PK]).toBeUndefined(); // No GSI1 if no license
      expect(item[ORGANIZATION_KEYS.GSI1SK]).toBeUndefined();

      // GSI2 should still be present for type listing
      expect(item[ORGANIZATION_KEYS.GSI2PK]).toEqual({
        S: `${ORGANIZATION_KEYS.TENANT_PREFIX}${tenantId}`,
      });
      expect(item[ORGANIZATION_KEYS.GSI2SK]).toEqual({
        S: `${ORGANIZATION_KEYS.TYPE_PREFIX}${orgType}#${ORGANIZATION_KEYS.ORG_PREFIX}${orgId}`,
      });
    });
  });

  describe("fromItem()", () => {
    const baseDynamoItem: any = {
      // PK and SK are not part of the fromItem logic directly but are for context
      tenantId: { S: tenantId },
      id: { S: orgId },
      name: { S: orgName },
      type: { S: orgType },
      status: { S: orgStatus },
      createdAt: { S: new Date().toISOString() }, // BaseEntity fields
      updatedAt: { S: new Date().toISOString() }, // BaseEntity fields
    };

    it("should deserialize a full DynamoDB item", () => {
      const fullDynamoItem = {
        ...baseDynamoItem,
        description: { S: "Full org description" },
        address: { S: "456 Main St" },
        contactEmail: { S: "<EMAIL>" },
        contactPhone: { S: "************" },
        website: { S: "http://full.example.com" },
        logoUrl: { S: "http://full.example.com/logo.png" },
        licenseNumber: { S: "LIC-FULL" },
      };
      const org = Organization.fromItem(fullDynamoItem);
      expect(org).toBeInstanceOf(Organization);
      expect(org.id).toBe(orgId);
      expect(org.name).toBe(orgName);
      expect(org.type).toBe(orgType);
      expect(org.status).toBe(orgStatus);
      expect(org.description).toBe("Full org description");
      expect(org.address).toBe("456 Main St");
      expect(org.contactEmail).toBe("<EMAIL>");
      expect(org.contactPhone).toBe("************");
      expect(org.website).toBe("http://full.example.com");
      expect(org.logoUrl).toBe("http://full.example.com/logo.png");
      expect(org.licenseNumber).toBe("LIC-FULL");
      expect(org.tenantId).toBe(tenantId);
      // Adjust timestamp checks - check for valid ISO string format instead of exact match
      expect(org.createdAt).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/
      );
      expect(org.updatedAt).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/
      );
      // Optionally check if dates are close to now if needed, but format check is often enough
    });

    it("should deserialize a minimal DynamoDB item", () => {
      const org = Organization.fromItem(baseDynamoItem);
      expect(org).toBeInstanceOf(Organization);
      expect(org.id).toBe(orgId);
      expect(org.description).toBeUndefined();
      expect(org.licenseNumber).toBeUndefined();
      expect(org.address).toBeUndefined();
    });

    it("should throw error if item is undefined", () => {
      expect(() => Organization.fromItem(undefined)).toThrow(
        "No organization item found!"
      );
    });

    it('should throw specific error if essential "S" attributes are missing or invalid', () => {
      const expectSpecificError = (
        malformedItem: any,
        expectedMessage: string
      ) => {
        expect(() => Organization.fromItem(malformedItem)).toThrow(
          expectedMessage
        );
      };

      // Test cases for each required field
      expectSpecificError(
        { ...baseDynamoItem, id: undefined },
        "Invalid item: Missing or invalid id"
      );
      expectSpecificError(
        { ...baseDynamoItem, id: { N: "123" } },
        "Invalid item: Missing or invalid id"
      );
      expectSpecificError(
        { ...baseDynamoItem, name: undefined },
        "Invalid item: Missing or invalid name"
      );
      expectSpecificError(
        { ...baseDynamoItem, name: { BOOL: true } },
        "Invalid item: Missing or invalid name"
      );
      expectSpecificError(
        { ...baseDynamoItem, type: undefined },
        "Invalid item: Missing or invalid type"
      );
      expectSpecificError(
        { ...baseDynamoItem, type: { S: "INVALID_TYPE" } },
        "Invalid item: Missing or invalid type"
      );
      expectSpecificError(
        { ...baseDynamoItem, status: undefined },
        "Invalid item: Missing or invalid status"
      );
      expectSpecificError(
        { ...baseDynamoItem, status: { S: "INVALID_STATUS" } },
        "Invalid item: Missing or invalid status"
      );
      expectSpecificError(
        { ...baseDynamoItem, tenantId: undefined },
        "Invalid item: Missing or invalid tenantId"
      );
      expectSpecificError(
        { ...baseDynamoItem, tenantId: { N: "456" } },
        "Invalid item: Missing or invalid tenantId"
      );
    });
  });

  describe("getById()", () => {
    it("should retrieve an organization successfully", async () => {
      const mockItem = {
        [ORGANIZATION_KEYS.FIELD_ID]: { S: orgId },
        [ORGANIZATION_KEYS.FIELD_TENANT_ID]: { S: tenantId },
        [ORGANIZATION_KEYS.FIELD_NAME]: { S: orgName },
        [ORGANIZATION_KEYS.FIELD_TYPE]: { S: orgType },
        [ORGANIZATION_KEYS.FIELD_STATUS]: { S: orgStatus },
      };
      mockGetItemPromise.mockResolvedValueOnce({ Item: mockItem });

      const org = await Organization.getById(tenantId, orgId);

      expect(getClient().getItem).toHaveBeenCalledWith({
        TableName: "test-table", // Expect "test-table"
        Key: {
          PK: { S: `${ORGANIZATION_KEYS.TENANT_PREFIX}${tenantId}` },
          SK: { S: `${ORGANIZATION_KEYS.ORG_PREFIX}${orgId}` },
        },
      });
      expect(org).toBeInstanceOf(Organization);
      expect(org.id).toBe(orgId);
      expect(org.name).toBe(orgName);
    });

    it("should throw error if tenantId is missing", async () => {
      // @ts-expect-error Testing invalid input
      await expect(Organization.getById(null, orgId)).rejects.toThrow(
        "Tenant ID is required"
      );
    });

    it("should throw error if organizationId is missing", async () => {
      // @ts-expect-error Testing invalid input
      await expect(Organization.getById(tenantId, null)).rejects.toThrow(
        "Organization ID is required"
      );
    });

    it("should throw error if organization not found", async () => {
      mockGetItemPromise.mockResolvedValueOnce({ Item: undefined });
      await expect(
        Organization.getById(tenantId, "non-existent-id")
      ).rejects.toThrow("Organization not found: non-existent-id");
    });

    it("should throw an error if DynamoDB getItem fails", async () => {
      const dbError = new Error("DynamoDB error");
      mockGetItemPromise.mockRejectedValueOnce(dbError);
      await expect(Organization.getById(tenantId, orgId)).rejects.toThrow(
        dbError
      );
    });
  });

  describe("listByType()", () => {
    const mockOrgItem = (
      id: string,
      itemOrgType: OrganizationType = orgType
    ) => ({
      // Include necessary fields for fromItem to work
      [ORGANIZATION_KEYS.FIELD_ID]: { S: id },
      [ORGANIZATION_KEYS.FIELD_TENANT_ID]: { S: tenantId },
      [ORGANIZATION_KEYS.FIELD_NAME]: { S: `Org ${id}` },
      [ORGANIZATION_KEYS.FIELD_TYPE]: { S: itemOrgType },
      [ORGANIZATION_KEYS.FIELD_STATUS]: { S: orgStatus },
      createdAt: { S: new Date().toISOString() }, // Use string literal
      updatedAt: { S: new Date().toISOString() }, // Use string literal
    });

    it("should list organizations by type successfully", async () => {
      const items = [
        mockOrgItem("org1", orgType),
        mockOrgItem("org2", orgType),
      ];
      mockQueryPromise.mockResolvedValueOnce({ Items: items });

      const orgs = await Organization.listByType(tenantId, orgType);

      expect(getClient().query).toHaveBeenCalledWith({
        TableName: "test-table", // Expect "test-table"
        IndexName: "GSI2",
        KeyConditionExpression:
          "GSI2PK = :pk AND begins_with(GSI2SK, :typePrefix)",
        ExpressionAttributeValues: {
          ":pk": { S: `${ORGANIZATION_KEYS.TENANT_PREFIX}${tenantId}` },
          ":typePrefix": { S: `${ORGANIZATION_KEYS.TYPE_PREFIX}${orgType}#` },
        },
      });
      expect(orgs.length).toBe(2);
      expect(orgs[0].id).toBe("org1");
      expect(orgs[1].type).toBe(orgType);
    });

    it("should return an empty array if no organizations match the type", async () => {
      mockQueryPromise.mockResolvedValueOnce({ Items: [] });
      const orgs = await Organization.listByType(
        tenantId,
        "PROPERTY_MANAGEMENT"
      );
      expect(orgs).toEqual([]);
    });

    it("should throw an error if tenantId is missing", async () => {
      // @ts-expect-error Testing invalid input
      await expect(Organization.listByType(null, orgType)).rejects.toThrow(
        "Tenant ID is required"
      );
    });

    it("should throw an error if type is missing", async () => {
      // @ts-expect-error Testing invalid input
      await expect(Organization.listByType(tenantId, null)).rejects.toThrow(
        "Organization type is required"
      );
    });

    it("should throw an error if DynamoDB query fails", async () => {
      const dbError = new Error("DynamoDB query error");
      mockQueryPromise.mockRejectedValueOnce(dbError);
      await expect(Organization.listByType(tenantId, orgType)).rejects.toThrow(
        dbError
      );
    });
  });

  describe("listByStatus()", () => {
    const mockOrgItemForScan = (
      id: string,
      currentStatus: OrganizationStatus
    ) => ({
      [ORGANIZATION_KEYS.FIELD_ID]: { S: id },
      [ORGANIZATION_KEYS.FIELD_TENANT_ID]: { S: tenantId },
      [ORGANIZATION_KEYS.FIELD_NAME]: { S: `Org ${id}` },
      [ORGANIZATION_KEYS.FIELD_TYPE]: { S: orgType },
      [ORGANIZATION_KEYS.FIELD_STATUS]: { S: currentStatus },
      createdAt: { S: new Date().toISOString() }, // Use string literal
      updatedAt: { S: new Date().toISOString() }, // Use string literal
      // PK is needed for the filter expression
      PK: { S: `${ORGANIZATION_KEYS.TENANT_PREFIX}${tenantId}` },
    });

    it("should list organizations by status successfully using scan", async () => {
      const items = [
        mockOrgItemForScan("orgActive1", "ACTIVE"),
        mockOrgItemForScan("orgActive2", "ACTIVE"),
      ];
      mockScanPromise.mockResolvedValueOnce({ Items: items });

      const activeOrgs = await Organization.listByStatus(tenantId, "ACTIVE");

      expect(getClient().scan).toHaveBeenCalledWith({
        TableName: "test-table", // Expect "test-table"
        FilterExpression:
          "begins_with(PK, :tenantPrefix) AND #status = :status",
        ExpressionAttributeNames: {
          "#status": "status",
        },
        ExpressionAttributeValues: {
          ":tenantPrefix": {
            S: `${ORGANIZATION_KEYS.TENANT_PREFIX}${tenantId}`,
          },
          ":status": { S: "ACTIVE" },
        },
      });
      expect(activeOrgs.length).toBe(2);
      expect(activeOrgs[0].status).toBe("ACTIVE");
    });

    it("should return an empty array if no organizations match the status", async () => {
      mockScanPromise.mockResolvedValueOnce({ Items: [] });
      const inactiveOrgs = await Organization.listByStatus(
        tenantId,
        "INACTIVE"
      );
      expect(inactiveOrgs).toEqual([]);
    });

    it("should throw an error if tenantId is missing for listByStatus", async () => {
      // @ts-expect-error Testing invalid input
      await expect(Organization.listByStatus(null, "ACTIVE")).rejects.toThrow(
        "Tenant ID is required"
      );
    });

    it("should throw an error if status is missing for listByStatus", async () => {
      // @ts-expect-error Testing invalid input
      await expect(Organization.listByStatus(tenantId, null)).rejects.toThrow(
        "Organization status is required"
      );
    });

    it("should throw an error if DynamoDB scan fails", async () => {
      const dbError = new Error("DynamoDB scan error");
      mockScanPromise.mockRejectedValueOnce(dbError);
      await expect(
        Organization.listByStatus(tenantId, "ACTIVE")
      ).rejects.toThrow(dbError);
    });
  });

  describe("getByLicense()", () => {
    const licNum = "LIC123XYZ";
    const mockLicenseItem = {
      [ORGANIZATION_KEYS.FIELD_ID]: { S: "org-license-test" },
      [ORGANIZATION_KEYS.FIELD_TENANT_ID]: { S: tenantId },
      [ORGANIZATION_KEYS.FIELD_NAME]: { S: "License Test Org" },
      [ORGANIZATION_KEYS.FIELD_TYPE]: { S: "SERVICE_PROVIDER" },
      [ORGANIZATION_KEYS.FIELD_STATUS]: { S: "ACTIVE" },
      [ORGANIZATION_KEYS.FIELD_LICENSE]: { S: licNum },
      createdAt: { S: new Date().toISOString() }, // Use string literal
      updatedAt: { S: new Date().toISOString() }, // Use string literal
    };

    it("should retrieve an organization by license successfully", async () => {
      mockQueryPromise.mockResolvedValueOnce({ Items: [mockLicenseItem] });

      const org = await Organization.getByLicense(licNum);

      expect(getClient().query).toHaveBeenCalledWith({
        TableName: "test-table", // Expect "test-table"
        IndexName: "GSI1",
        KeyConditionExpression: "GSI1PK = :licensePk",
        ExpressionAttributeValues: {
          ":licensePk": { S: `LICENSE#${licNum}` },
        },
      });
      expect(org).toBeInstanceOf(Organization);
      expect(org!.id).toBe("org-license-test");
      expect(org!.licenseNumber).toBe(licNum);
    });

    it("should return null if no organization found for the license", async () => {
      mockQueryPromise.mockResolvedValueOnce({ Items: [] });
      const org = await Organization.getByLicense("NON-EXISTENT-LIC");
      expect(org).toBeNull();
    });

    it("should throw an error if licenseNumber is missing", async () => {
      // @ts-expect-error Testing invalid input
      await expect(Organization.getByLicense(null)).rejects.toThrow(
        "License number is required"
      );
    });

    it("should throw an error if DynamoDB query for license fails", async () => {
      const dbError = new Error("DynamoDB license query error");
      mockQueryPromise.mockRejectedValueOnce(dbError);
      await expect(Organization.getByLicense(licNum)).rejects.toThrow(dbError);
    });
  });

  describe("create() (inherited from BaseEntity)", () => {
    let orgToCreate: Organization;
    const expectedItemMatcher = (actualItem: Record<string, any>) => {
      // Check core fields
      expect(actualItem.PK).toEqual({ S: orgToCreate.pk });
      expect(actualItem.SK).toEqual({ S: orgToCreate.sk });
      expect(actualItem.tenantId).toEqual({ S: orgToCreate.tenantId });
      expect(actualItem.id).toEqual({ S: orgToCreate.id });
      expect(actualItem.name).toEqual({ S: orgToCreate.name });
      expect(actualItem.type).toEqual({ S: orgToCreate.type });
      expect(actualItem.status).toEqual({ S: orgToCreate.status });

      // Check timestamps (existence and roughly correct)
      expect(actualItem.createdAt.S).toBeDefined();
      expect(actualItem.updatedAt.S).toBeDefined();
      expect(actualItem.createdAt.S).toEqual(actualItem.updatedAt.S);
      expect(new Date(actualItem.createdAt.S).getTime()).toBeCloseTo(
        new Date(orgToCreate.createdAt).getTime(),
        -2 // a few ms tolerance
      );
      return true; // if all checks pass
    };

    beforeEach(() => {
      orgToCreate = new Organization(
        tenantId,
        orgId, // Use predefined orgId for predictability in PK/SK
        "New Org For Create",
        "AUTHORITY",
        "PENDING"
      );
    });

    it("should create an organization item successfully via BaseEntity.create", async () => {
      mockPutItemPromise.mockResolvedValueOnce({}); // Simulate successful put

      // Freeze time for createdAt/updatedAt consistency
      const now = new Date();
      jest.useFakeTimers().setSystemTime(now);

      const createdOrg = await orgToCreate.create(); // Calls BaseEntity.create()

      // Construct the expected item based on orgToCreate state AFTER create()
      // (which updates timestamps)
      const expectedItem = orgToCreate.toItem();

      expect(getClient().putItem).toHaveBeenCalledWith({
        TableName: "test-table", // Expect "test-table"
        Item: expectedItem,
        ConditionExpression: "attribute_not_exists(PK)",
      });
      expect(createdOrg).toBe(orgToCreate); // Should return the same instance
      expect(createdOrg.createdAt).toEqual(now.toISOString());
      expect(createdOrg.updatedAt).toEqual(now.toISOString());

      jest.useRealTimers();
    });

    it("should throw an error if DynamoDB putItem fails (e.g., conditional check fail)", async () => {
      const dbError = new Error("ConditionalCheckFailedException");
      mockPutItemPromise.mockRejectedValueOnce(dbError);

      await expect(orgToCreate.create()).rejects.toThrow(dbError);
    });
  });

  describe("update() (overridden)", () => {
    let orgToUpdate: Organization;
    const baseUpdateSpy = jest.spyOn(BaseEntity.prototype, "update");

    beforeEach(() => {
      orgToUpdate = new Organization(
        tenantId,
        orgId,
        orgName,
        orgType,
        "ACTIVE",
        { description: "Initial Description" }
      );
      // Reset spy for each test
      baseUpdateSpy.mockClear();
    });

    it("should update timestamps and call super.update()", async () => {
      baseUpdateSpy.mockResolvedValueOnce(orgToUpdate); // Mock super.update() success

      const originalUpdatedAt = orgToUpdate.updatedAt;
      await new Promise((resolve) => setTimeout(resolve, 1)); // Ensure time passes

      orgToUpdate.name = "Updated Org Name"; // Make a change
      const updatedOrg = await orgToUpdate.update();

      expect(updatedOrg.updatedAt).not.toBe(originalUpdatedAt);
      expect(updatedOrg.name).toBe("Updated Org Name");
      expect(baseUpdateSpy).toHaveBeenCalledTimes(1);
      expect(updatedOrg).toBe(orgToUpdate); // Returns the instance
    });

    it("should throw an error if super.update() fails", async () => {
      const updateError = new Error("Super update failed");
      baseUpdateSpy.mockRejectedValueOnce(updateError);

      orgToUpdate.description = "Attempted update";
      await expect(orgToUpdate.update()).rejects.toThrow(updateError);
      // Timestamps should still be updated in memory before the failed save
      // This depends on the implementation detail of updateTimestamp() placement
      // Assuming updateTimestamp() is called before super.update()
    });
  });

  // Test convenience functions if they add significant logic or error handling
  // For now, they are direct pass-throughs, so extensive testing here might be redundant
  // if the class static methods are thoroughly tested.

  describe("Convenience Functions", () => {
    // Example for createOrganization - others would be similar
    describe("createOrganization()", () => {
      it("should call organization.create()", async () => {
        const org = new Organization(
          tenantId,
          "temp-id",
          "Temp Org",
          "AUTHORITY"
        );
        const mockCreate = jest.spyOn(org, "create").mockResolvedValueOnce(org);

        await createOrganization(org);
        expect(mockCreate).toHaveBeenCalled();
        mockCreate.mockRestore();
      });
    });

    describe("getOrganization()", () => {
      it("should call Organization.getById()", async () => {
        const mockGetById = jest
          .spyOn(Organization, "getById")
          .mockResolvedValueOnce({} as Organization);
        await getOrganization(tenantId, orgId);
        expect(mockGetById).toHaveBeenCalledWith(tenantId, orgId);
        mockGetById.mockRestore();
      });
    });

    describe("listOrganizations()", () => {
      it("should call Organization.listByType()", async () => {
        const mockListByType = jest
          .spyOn(Organization, "listByType")
          .mockResolvedValueOnce([]);
        await listOrganizations(tenantId, "SERVICE_PROVIDER");
        expect(mockListByType).toHaveBeenCalledWith(
          tenantId,
          "SERVICE_PROVIDER"
        );
        mockListByType.mockRestore();
      });
    });

    describe("getOrganizationByLicense()", () => {
      it("should call Organization.getByLicense()", async () => {
        const mockGetByLicense = jest
          .spyOn(Organization, "getByLicense")
          .mockResolvedValueOnce(null);
        const license = "LIC-TEST-CONV";
        await getOrganizationByLicense(license);
        expect(mockGetByLicense).toHaveBeenCalledWith(license);
        mockGetByLicense.mockRestore();
      });
    });
  });
});

// Helper function from BaseEntity tests - might be useful
// export const expectItemToMatchObject = (
//   item: DynamoDB.AttributeMap,
//   obj: Record<string, any>,
//   ignoreFields: string[] = []
// ) => {
//   for (const key in obj) {
//     if (ignoreFields.includes(key) || obj[key] === undefined) continue;
//     expect(item[key]).toBeDefined();
//     // Basic check, extend for types like N, BOOL, M, L as needed
//     if (item[key]?.S !== undefined) {
//       expect(item[key].S).toEqual(obj[key]);
//     } else if (item[key]?.N !== undefined) {
//       expect(item[key].N).toEqual(obj[key].toString());
//     }
//     // Add more type checks if necessary
//   }
// };

// Example of a custom matcher for the create() test
// This allows more flexible checking of the item sent to putItem,
// especially for dynamic values like timestamps.
expect.extend({
  toMatchOrganizationItem(received, expectedOrg: Organization) {
    const { isMatch, message } = checkItem(received, expectedOrg);
    if (isMatch) {
      return {
        message: () =>
          `expected received not to match organization ${expectedOrg.id}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected received to match organization ${expectedOrg.id}. ${message}`,
        pass: false,
      };
    }
  },
});

// Simplified check function for the custom matcher
// Adjust as needed for full coverage
function checkItem(receivedItem: any, expectedOrg: Organization) {
  if (!receivedItem || typeof receivedItem !== "object") {
    return { isMatch: false, message: "Received item is not an object." };
  }

  const expectedSerialized = expectedOrg.toItem();

  for (const key of Object.keys(expectedSerialized)) {
    if (key === "createdAt" || key === "updatedAt") {
      if (!receivedItem[key] || !receivedItem[key].S) {
        return { isMatch: false, message: `Missing timestamp ${key}` };
      }
      // For timestamps, just check if they are valid date strings (roughly)
      // or compare with more precision if needed
      if (isNaN(new Date(receivedItem[key].S).getTime())) {
        return { isMatch: false, message: `Invalid date string for ${key}` };
      }
      // Check if it's very close to the expected org's timestamp
      const timeDiff = Math.abs(
        new Date(receivedItem[key].S).getTime() -
          new Date((expectedSerialized[key] as any).S).getTime()
      );
      if (timeDiff > 5000) {
        // Allow up to 5s difference for test timing
        return {
          isMatch: false,
          message: `Timestamp ${key} (${receivedItem[key].S}) differs too much from expected (${(expectedSerialized[key] as any).S})`,
        };
      }
    } else {
      if (
        JSON.stringify(receivedItem[key]) !==
        JSON.stringify(expectedSerialized[key])
      ) {
        return {
          isMatch: false,
          message: `Field ${key} does not match. Expected: ${JSON.stringify(expectedSerialized[key])}, Received: ${JSON.stringify(receivedItem[key])}`,
        };
      }
    }
  }
  return { isMatch: true };
}

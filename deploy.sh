#!/bin/bash

# CLKK Payment System Deployment Script
# This script handles layer optimization and deployment

set -e

echo "🚀 CLKK Payment System Deployment"
echo "=================================="

# Check if environment is provided
ENVIRONMENT=${1:-dev}
echo "📋 Environment: $ENVIRONMENT"

# Step 1: Clean and optimize commons layer
echo "🧹 Step 1: Cleaning commons layer..."
cd layers/commons

# Remove node_modules and package-lock.json to start fresh
rm -rf node_modules package-lock.json

# Install only production dependencies
echo "📦 Installing optimized dependencies..."
npm ci --production --no-optional

# Remove unnecessary files to reduce size
echo "🗑️  Removing unnecessary files..."
find node_modules -name "*.md" -delete
find node_modules -name "*.txt" -delete
find node_modules -name "CHANGELOG*" -delete
find node_modules -name "LICENSE*" -delete
find node_modules -name "*.map" -delete
find node_modules -name "*.d.ts" -delete
find node_modules -name "test" -type d -exec rm -rf {} + 2>/dev/null || true
find node_modules -name "tests" -type d -exec rm -rf {} + 2>/dev/null || true
find node_modules -name "docs" -type d -exec rm -rf {} + 2>/dev/null || true
find node_modules -name "examples" -type d -exec rm -rf {} + 2>/dev/null || true

# Check layer size
echo "📏 Checking layer size..."
LAYER_SIZE=$(du -sh . | cut -f1)
echo "Layer size: $LAYER_SIZE"

cd ../..

# Step 2: Install lambda dependencies
echo "📦 Step 2: Installing lambda dependencies..."

LAMBDA_DIRS=(
    "lambdas/payments/create-cashapp-payment"
    "lambdas/payments/check-cashapp-payment-status"
    "lambdas/clerk-webhooks"
    "lambdas/auth/clerk-authorizer"
    "lambdas/applications/submit-application"
    "lambdas/applications/approve-application"
    "lambdas/organizations/create-organization"
    "lambdas/invitations/send-invitation"
)

for dir in "${LAMBDA_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "Installing dependencies for $dir..."
        cd "$dir"
        npm ci --production --no-optional
        cd - > /dev/null
    else
        echo "⚠️  Warning: Directory $dir not found"
    fi
done

# Step 3: Build with SAM
echo "🔨 Step 3: Building with SAM..."
sam build --use-container --cached

# Step 4: Deploy
echo "🚀 Step 4: Deploying to $ENVIRONMENT..."

# Check if required parameters are set
if [ -z "$CLERK_API_KEY" ]; then
    echo "❌ Error: CLERK_API_KEY environment variable is required"
    echo "Set it with: export CLERK_API_KEY='your-clerk-api-key'"
    exit 1
fi

if [ -z "$CLERK_WEBHOOK_SECRET" ]; then
    echo "❌ Error: CLERK_WEBHOOK_SECRET environment variable is required"
    echo "Set it with: export CLERK_WEBHOOK_SECRET='your-webhook-secret'"
    exit 1
fi

if [ -z "$POCKETKNIGHTS_API_TOKEN" ]; then
    echo "❌ Error: POCKETKNIGHTS_API_TOKEN environment variable is required"
    echo "Set it with: export POCKETKNIGHTS_API_TOKEN='your-api-token'"
    exit 1
fi

if [ -z "$CASHAPP_CLIENT_ID" ]; then
    echo "❌ Error: CASHAPP_CLIENT_ID environment variable is required"
    echo "Set it with: export CASHAPP_CLIENT_ID='your-client-id'"
    exit 1
fi

if [ -z "$CASHAPP_BRAND_ID" ]; then
    echo "❌ Error: CASHAPP_BRAND_ID environment variable is required"
    echo "Set it with: export CASHAPP_BRAND_ID='your-brand-id'"
    exit 1
fi

# Deploy with parameters
sam deploy \
  --template-file template.yaml \
  --stack-name "clkk-payment-system-$ENVIRONMENT" \
  --capabilities CAPABILITY_IAM CAPABILITY_AUTO_EXPAND \
  --parameter-overrides \
    Environment="$ENVIRONMENT" \
    ClerkJwtPublicKeySsmParamPath="/clkk/clerk/jwt-public-key" \
    ClerkApiKey="$CLERK_API_KEY" \
    ClerkWebhookSecret="$CLERK_WEBHOOK_SECRET" \
    PocketKnightsBaseUrl="https://api.pocketknights.com" \
    PocketKnightsApiToken="$POCKETKNIGHTS_API_TOKEN" \
    CashAppClientId="$CASHAPP_CLIENT_ID" \
    CashAppBrandId="$CASHAPP_BRAND_ID" \
    CashAppMerchantNo="${CASHAPP_MERCHANT_NO:-default}" \
    CashAppScriptUrl="${CASHAPP_SCRIPT_URL:-https://sandbox.kit.cash.app/v1/pay.js}" \
  --region "${AWS_REGION:-us-east-1}" \
  --confirm-changeset \
  --resolve-s3

echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Check the API endpoint in the CloudFormation outputs"
echo "2. Test the payment endpoints"
echo "3. Configure Clerk webhooks to point to your API"
echo ""
echo "🔗 Get API endpoint:"
echo "aws cloudformation describe-stacks --stack-name clkk-payment-system-$ENVIRONMENT --query 'Stacks[0].Outputs[?OutputKey==\`PaymentApiEndpoint\`].OutputValue' --output text" 
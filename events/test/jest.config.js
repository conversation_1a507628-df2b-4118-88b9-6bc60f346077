/** @type {import('ts-jest/dist/types').InitialOptionsTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  moduleNameMapper: {
    // Use relative paths for imports
    '^@/commons/(.*)$': '<rootDir>/../../layers/commons/$1',
    '^@/lambdas/(.*)$': '<rootDir>/../../lambdas/$1'
  },
  moduleFileExtensions: ['ts', 'js', 'json', 'node'],
  testMatch: ['**/*.test.ts'],
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/tsconfig.json'
    }
  },
  roots: [
    '<rootDir>',
    '<rootDir>/../../lambdas',
    '<rootDir>/../../layers'
  ],
  modulePaths: [
    '<rootDir>',
    '<rootDir>/../../'
  ],
  transform: {
    '^.+\\.ts$': 'ts-jest'
  },
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js']
}; 
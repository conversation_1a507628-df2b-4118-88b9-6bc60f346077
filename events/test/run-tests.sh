#!/bin/bash

# Script to run integration tests for the FireGuard API

set -e  # Exit on error

# Print current AWS identity for verification
echo "Running as AWS identity:"
aws sts get-caller-identity

# Set environment variables for testing
export NODE_ENV=test
export TABLE_NAME=dev-FireGuardTable  # Replace with your actual test table name
export AWS_REGION=us-east-1  # Replace with your actual region
export TEST_RUN_ID=$(date +%s)  # Generate a unique ID for this test run

# Install dependencies if needed
echo "Installing dependencies..."
cd "$(dirname "$0")"  # Move to the script directory
npm install

# Run the tests
echo "Running tests..."
npm test

# Clean up any test data if needed
echo "Tests completed. Any cleanup will happen in the afterAll blocks." 
{"compilerOptions": {"target": "es2019", "module": "commonjs", "lib": ["es2019", "es2020"], "declaration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "esModuleInterop": true, "typeRoots": ["./node_modules/@types"], "baseUrl": ".", "paths": {"@/commons/*": ["../../layers/commons/*"], "@/lambdas/*": ["../../lambdas/*"]}}, "exclude": ["node_modules", "cdk.out"], "include": ["./**/*.ts", "../../lambdas/**/*.ts", "../../layers/**/*.ts"]}
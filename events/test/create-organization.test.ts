import { describe, it, expect, beforeEach, afterAll } from '@jest/globals';
import { createApiGatewayEvent, cleanupTestData, generateTestId } from './test-utils';

// Import the handler directly using relative path
// Make sure this path is correct - adjust according to your project structure
import { handler } from '../../lambdas/organizations/create-organization/index';

describe('Create Organization Integration Tests', () => {
  const testIds: { PK: string; SK: string }[] = [];
  const testId = generateTestId();
  
  // Clean up test data after tests
  afterAll(async () => {
    await cleanupTestData(testIds);
  });

  // Test for invalid JSON
  it('should return 400 Bad Request when JSON is invalid', async () => {
    const event = createApiGatewayEvent({
      path: '/organizations',
      method: 'POST',
      body: '{invalid-json',
      headers: {
        'Content-Type': 'application/json'
      },
      requestContext: {
        authorizer: {
          claims: {
            sub: `user-${testId}`,
            email: `test-${testId}@example.com`
          }
        }
      }
    });

    const response = await handler(event);
    
    expect(response.statusCode).toBe(400);
    const body = JSON.parse(response.body);
    expect(body.message).toBeDefined();
  });

  // If you don't want to use direct imports due to module resolution issues, 
  // you can use a simplified approach:
  it('should validate input (simplified test)', async () => {
    // Create a mock handler that only tests JSON parsing
    const mockHandler = async (event: any) => {
      try {
        if (!event.body) {
          return {
            statusCode: 400,
            body: JSON.stringify({ message: "Request body is missing." })
          };
        }
        
        // Test the JSON parsing functionality
        JSON.parse(event.body);
        
        // If parsing succeeds, return a dummy success response
        return {
          statusCode: 200,
          body: JSON.stringify({ success: true })
        };
      } catch (error) {
        // If parsing fails, return a 400 response
        return {
          statusCode: 400,
          body: JSON.stringify({ message: "Invalid JSON format." })
        };
      }
    };
    
    // Create an event with invalid JSON
    const event = createApiGatewayEvent({
      path: '/organizations',
      method: 'POST',
      body: '{invalid-json',
      requestContext: {
        authorizer: {
          claims: {
            sub: `user-${testId}`,
            email: `test-${testId}@example.com`
          }
        }
      }
    });
    
    // Call our mock handler
    const response = await mockHandler(event);
    
    // Verify it returns a 400 status code
    expect(response.statusCode).toBe(400);
    
    // Verify it contains the expected error message
    const body = JSON.parse(response.body);
    expect(body.message).toContain('Invalid JSON format');
  });
}); 
import { DynamoDB } from 'aws-sdk';
import * as AWS from 'aws-sdk';
import { APIGatewayProxyEvent } from 'aws-lambda';

// Configure AWS SDK
AWS.config.update({
  region: process.env.AWS_REGION || 'us-east-1'
});

// Create DynamoDB client
export const dynamoDb = new DynamoDB.DocumentClient();
export const tableName = process.env.TABLE_NAME || 'dev-FireGuardTable';

/**
 * Creates a mock API Gateway event
 * @param path - API path
 * @param method - HTTP method
 * @param body - Request body
 * @param headers - Request headers
 * @param queryStringParameters - Query parameters
 * @returns APIGatewayProxyEvent
 */
export function createApiGatewayEvent({
  path = '/',
  method = 'POST',
  body,
  headers = {},
  queryStringParameters = {},
  pathParameters = {},
  requestContext = {}
}: {
  path?: string;
  method?: string;
  body?: any;
  headers?: Record<string, string>;
  queryStringParameters?: Record<string, string>;
  pathParameters?: Record<string, string>;
  requestContext?: any;
}): APIGatewayProxyEvent {
  return {
    body: body ? (typeof body === 'string' ? body : JSON.stringify(body)) : null,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    },
    multiValueHeaders: {},
    httpMethod: method,
    isBase64Encoded: false,
    path,
    pathParameters: pathParameters || null,
    queryStringParameters: queryStringParameters || null,
    multiValueQueryStringParameters: null,
    stageVariables: null,
    requestContext: {
      accountId: '************',
      apiId: 'api-id',
      authorizer: null,
      domainName: 'example.com',
      domainPrefix: 'example',
      extendedRequestId: 'request-id',
      httpMethod: method,
      identity: {
        accessKey: null,
        accountId: null,
        caller: null,
        cognitoAuthenticationProvider: null,
        cognitoAuthenticationType: null,
        cognitoIdentityId: null,
        cognitoIdentityPoolId: null,
        principalOrgId: null,
        sourceIp: '127.0.0.1',
        user: null,
        userAgent: 'Custom User Agent String',
        userArn: null
      },
      path,
      protocol: 'HTTP/1.1',
      requestId: 'request-id',
      requestTime: '04/Mar/2020:19:15:17 +0000',
      requestTimeEpoch: *************,
      resourceId: 'resource-id',
      resourcePath: path,
      stage: 'test',
      ...requestContext
    },
    resource: path
  };
}

/**
 * Clean up test data from DynamoDB
 * @param keys - Array of keys to delete
 */
export async function cleanupTestData(keys: { PK: string; SK: string }[]): Promise<void> {
  for (const key of keys) {
    await dynamoDb.delete({
      TableName: tableName,
      Key: key
    }).promise();
  }
}

/**
 * Generate a unique test identifier to prevent test collisions
 */
export function generateTestId(): string {
  return `test-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
}

/**
 * Verify if an item exists in DynamoDB
 * @param key - Key to check
 * @returns boolean indicating if item exists
 */
export async function itemExists(key: { PK: string; SK: string }): Promise<boolean> {
  const result = await dynamoDb.get({
    TableName: tableName,
    Key: key
  }).promise();
  
  return !!result.Item;
}

/**
 * Get an item from DynamoDB
 * @param key - Key to get
 * @returns The item or null if not found
 */
export async function getItem(key: { PK: string; SK: string }): Promise<any> {
  const result = await dynamoDb.get({
    TableName: tableName,
    Key: key
  }).promise();
  
  return result.Item || null;
} 
// Set up test environment
process.env.NODE_ENV = 'test';
process.env.TABLE_NAME = process.env.TABLE_NAME || 'dev-FireGuardTable';
process.env.AWS_REGION = process.env.AWS_REGION || 'us-east-1';

// Configure jest timeout
jest.setTimeout(10000); // 10 seconds

// AWS Config for tests
const AWS = require('aws-sdk');
AWS.config.update({
  region: process.env.AWS_REGION,
  // Use local credentials for tests
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || 'test',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'test'
  }
});

// Optional: Mock AWS services as needed
// For example:
// jest.mock('aws-sdk', () => {
//   const mockDynamoDB = {
//     get: jest.fn().mockReturnValue({
//       promise: jest.fn().mockResolvedValue({ Item: {} })
//     }),
//     put: jest.fn().mockReturnValue({
//       promise: jest.fn().mockResolvedValue({})
//     })
//   };
//   return {
//     DynamoDB: {
//       DocumentClient: jest.fn(() => mockDynamoDB)
//     }
//   };
// });

// Set up any global variables or mocks needed for tests
global.console.log = jest.fn(); // Optional: silence logs during tests 
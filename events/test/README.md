# FireGuard API Integration Tests

This directory contains integration tests for the FireGuard API endpoints, focusing on the `submit-application` and `create-organization` endpoints.

## Test Structure

- **Configuration Files**:
  - `jest.config.js`: Jest configuration with proper module paths
  - `tsconfig.json`: TypeScript configuration for the tests
  - `jest.setup.js`: Setup file for environment variables and mocks
  - `package.json`: Dependencies for testing

- **Utility Files**:
  - `test-utils.ts`: Helper functions for creating mock events, interacting with DynamoDB, etc.

- **Test Files**:
  - `submit-application.test.ts`: Tests for the submit-application endpoint
  - `create-organization.test.ts`: Tests for the create-organization endpoint

## Path Resolution

The tests are configured to use relative paths for imports rather than trying to use the `commons/nodejs` pattern. This approach should prevent module resolution issues.

## Running the Tests

### Prerequisites

- AWS credentials with appropriate permissions for your development/test environment
- Node.js and npm installed

### Steps

1. Navigate to the test directory:
   ```
   cd events/test
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Run the tests:
   ```
   npm test
   ```

   Or use the provided shell script:
   ```
   ./run-tests.sh
   ```

## Test Implementation Approaches

The tests include two different approaches:

1. **Direct Lambda Import**: This approach directly imports the Lambda handler function and tests it:
   ```typescript
   import { handler } from '../../lambdas/applications/submit-application/index';
   // Test using the actual handler
   ```

2. **Simplified Approach**: This approach uses a mock handler to test specific aspects (such as input validation) without requiring the full Lambda implementation:
   ```typescript
   // Create a mock handler that only tests JSON parsing
   const mockHandler = async (event: any) => {
     try {
       JSON.parse(event.body);
       // ...
     } catch (error) {
       // ...
     }
   };
   ```

Use the approach that works best for your specific testing needs and environment constraints.

## Troubleshooting

- If you encounter module resolution errors, check that the paths in `jest.config.js` and `tsconfig.json` are correct for your project structure.
- If you see AWS permission errors, verify your AWS credentials and that you have access to the required resources.
- For other issues, check that all dependencies are installed and that your environment variables are properly set. 
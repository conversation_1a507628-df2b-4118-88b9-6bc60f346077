{"resource": "/buildings/{buildingId}", "path": "/buildings/building-123", "httpMethod": "PUT", "headers": {"Content-Type": "application/json", "Authorization": "Bearer YOUR_ACTUAL_JWT_TOKEN"}, "pathParameters": {"buildingId": "building-123"}, "requestContext": {"authorizer": {"userId": "user_2QUu0EFyJPzV49iVsEn1gXDGypV", "tenantId": "tenant_123456789"}}, "body": "{\"name\":\"Updated Building Name\",\"riskLevel\":\"HIGH\",\"notes\":\"Updated building notes\",\"fireProtectionSystems\":{\"fireAlarm\":{\"installed\":true,\"type\":\"Addressable with Voice Evacuation\"},\"sprinkler\":{\"installed\":true,\"type\":\"Pre-Action\"}}}"}
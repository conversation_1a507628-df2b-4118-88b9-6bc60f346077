openapi: "3.0.1"
info:
  title: "FireGuard API"
  version: "1.0.0"
  description: |
    # FireGuard API

    The FireGuard REST API provides comprehensive access to the FireGuard platform's resources and functionality.

    This API enables you to manage buildings, fire guards, organizations, and other critical fire safety resources.
    It's designed for integration with mobile applications, web dashboards, and third-party systems.

    ## Authentication

    All API requests require authentication using a JWT token. The token should be included in the Authorization header.

    ## Rate Limiting

    API requests are subject to rate limiting to ensure system stability. Please implement appropriate retry logic with exponential backoff.

    ## Versioning

    The API follows semantic versioning. Breaking changes will be introduced in major version updates.
  contact:
    name: "FireGuard Support Team"
    email: "<EMAIL>"
    url: "https://fireguard.com/support"
  license:
    name: "Proprietary"
    url: "https://fireguard.com/terms"
  termsOfService: "https://fireguard.com/terms"
tags:
  - name: Buildings
    description: "Endpoints for managing buildings and facilities"
  - name: Guards
    description: "Endpoints for managing fire guards and their assignments"
  - name: Organizations
    description: "Endpoints for managing organizations in the FireGuard system"
  - name: Applications
    description: "Endpoints for managing organization applications and approvals"
  - name: Webhooks
    description: "Endpoints for handling external service webhooks"
  - name: Authentication
    description: "Endpoints for authentication and authorization"
externalDocs:
  description: "FireGuard Documentation"
  url: "https://docs.fireguard.com"
security:
  - bearerAuth: []
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT token authentication. The token should be included in the Authorization header as "Bearer {token}".

        Tokens are issued through the authentication process and have a limited lifetime.
        When a token expires, the client should request a new one.
  parameters:
    PageSize:
      name: pageSize
      in: query
      description: |
        Number of items to return per page.

        The default is 20 items per page, and the maximum allowed is 100 items per page.
        Values larger than the maximum will be capped at the maximum.
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20
      required: false
    PageToken:
      name: pageToken
      in: query
      description: |
        Token for pagination. This token is returned in the `nextPageToken` field of the response.

        To get the next page of results, include this token in your next request.
        If this parameter is omitted, the first page of results is returned.
      schema:
        type: string
      required: false
    SortBy:
      name: sortBy
      in: query
      description: |
        Field to sort the results by.

        The available sort fields depend on the endpoint.
        If this parameter is omitted, the default sort field is used.
      schema:
        type: string
      required: false
    SortOrder:
      name: sortOrder
      in: query
      description: |
        Order to sort the results in.

        Use `asc` for ascending order or `desc` for descending order.
        If this parameter is omitted, the default is `desc` (newest first).
      schema:
        type: string
        enum: [asc, desc]
        default: desc
      required: false
  schemas:
    CreateBuildingRequest:
      type: object
      title: "CreateBuildingRequest"
      required:
        - name
        - address
        - organizationId
      properties:
        name:
          type: string
          description: Name of the building
        address:
          type: string
          description: Primary address of the building
        address2:
          type: string
          description: Secondary address information (optional)
        city:
          type: string
          description: City where the building is located
        state:
          type: string
          description: State where the building is located
        zip:
          type: string
          description: Postal/ZIP code
        organizationId:
          type: string
          description: ID of the organization that owns the building
        constructionType:
          type: string
          description: Type of building construction
          enum: [type1, type2, type3, type4, type5]
        occupancyClass:
          type: string
          description: Occupancy classification of the building
        squareFootage:
          type: number
          description: Total square footage of the building
        numberOfFloors:
          type: integer
          description: Number of floors in the building
        yearBuilt:
          type: integer
          description: Year the building was constructed
        fireProtectionSystems:
          type: object
          description: Fire protection systems in the building
          properties:
            fireAlarm:
              type: boolean
              description: Whether the building has a fire alarm system
            sprinkler:
              type: boolean
              description: Whether the building has a sprinkler system
            standpipe:
              type: boolean
              description: Whether the building has a standpipe system
        riskLevel:
          type: string
          description: Risk level of the building
          enum: [LOW, MEDIUM, HIGH, CRITICAL]
        notes:
          type: string
          description: Additional notes about the building

    BuildingResponse:
      type: object
      title: "BuildingResponse"
      properties:
        id:
          type: string
          description: The generated ID of the building
        name:
          type: string
          description: Name of the building
        createdAt:
          type: string
          format: date-time
          description: Timestamp when the building was created

    Building:
      type: object
      title: "Building"
      properties:
        id:
          type: string
        name:
          type: string
        organizationId:
          type: string
        address:
          type: string
        address2:
          type: string
        city:
          type: string
        state:
          type: string
        zip:
          type: string
        constructionType:
          type: string
        occupancyClass:
          type: string
        squareFootage:
          type: number
        numberOfFloors:
          type: integer
        yearBuilt:
          type: integer
        fireProtectionSystems:
          type: object
          properties:
            fireAlarm:
              type: boolean
            sprinkler:
              type: boolean
            standpipe:
              type: boolean
        riskLevel:
          type: string
        notes:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    UpdateGuardLocationRequest:
      type: object
      title: "UpdateGuardLocationRequest"
      required:
        - guardId
        - latitude
        - longitude
      properties:
        guardId:
          type: string
          description: ID of the guard to update
        latitude:
          type: number
          description: Latitude coordinate
        longitude:
          type: number
          description: Longitude coordinate
        timestamp:
          type: string
          format: date-time
          description: Optional timestamp for the location update (defaults to current time)
        accuracy:
          type: number
          description: Accuracy of the location in meters
        speed:
          type: number
          description: Speed in meters per second
        heading:
          type: number
          description: Heading in degrees (0-360)
        altitude:
          type: number
          description: Altitude in meters
        locationName:
          type: string
          description: Human-readable name of the location
        buildingId:
          type: string
          description: ID of the building if the guard is inside a building
        isInGeofence:
          type: boolean
          description: Whether the guard is within their assigned geofence
        geofenceId:
          type: string
          description: ID of the geofence the guard is in or should be in

    UpdateGuardLocationResponse:
      type: object
      title: "UpdateGuardLocationResponse"
      properties:
        guardId:
          type: string
          description: ID of the guard
        locationId:
          type: string
          description: ID of the created location history entry
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the location update
        isInGeofence:
          type: boolean
          description: Whether the guard is within their assigned geofence

    LocationHistoryResponse:
      type: object
      title: "LocationHistoryResponse"
      properties:
        items:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: ID of the location history entry
              guardId:
                type: string
                description: ID of the guard
              timestamp:
                type: string
                format: date-time
                description: Timestamp of the location update
              latitude:
                type: number
                description: Latitude coordinate
              longitude:
                type: number
                description: Longitude coordinate
              accuracy:
                type: number
                description: Accuracy of the location in meters
              speed:
                type: number
                description: Speed in meters per second
              heading:
                type: number
                description: Heading in degrees (0-360)
              altitude:
                type: number
                description: Altitude in meters
              buildingId:
                type: string
                description: ID of the building if the guard is inside a building
              isInGeofence:
                type: boolean
                description: Whether the guard is within their assigned geofence
              geofenceId:
                type: string
                description: ID of the geofence the guard is in or should be in
        pagination:
          type: object
          properties:
            lastEvaluatedKey:
              type: string
              description: Pagination token for retrieving next page of results
            hasMore:
              type: boolean
              description: Whether there are more results available

    GuardsOutsideGeofenceResponse:
      type: object
      title: "GuardsOutsideGeofenceResponse"
      properties:
        items:
          type: array
          items:
            type: object
            properties:
              guardId:
                type: string
                description: ID of the guard
              guardName:
                type: string
                description: Name of the guard
              timestamp:
                type: string
                format: date-time
                description: Timestamp of the location update
              latitude:
                type: number
                description: Latitude coordinate
              longitude:
                type: number
                description: Longitude coordinate
              buildingId:
                type: string
                description: ID of the building if the guard is inside a building
              locationId:
                type: string
                description: ID of the location history entry
              lastActiveAt:
                type: string
                format: date-time
                description: Timestamp when the guard was last active

    CreateFireGuardRequest:
      type: object
      title: "CreateFireGuardRequest"
      required:
        - name
      properties:
        name:
          type: string
          description: Name of the fire guard
        email:
          type: string
          description: Email address of the fire guard
        phone:
          type: string
          description: Phone number of the fire guard
        employeeId:
          type: string
          description: Employee ID of the fire guard
        certificationNumber:
          type: string
          description: Certification number of the fire guard
        certificationExpiry:
          type: string
          format: date-time
          description: Expiration date of the fire guard's certification
        isActive:
          type: boolean
          description: Whether the fire guard is active
          default: true
        notes:
          type: string
          description: Additional notes about the fire guard
        assignedBuildingIds:
          type: array
          items:
            type: string
          description: IDs of buildings assigned to the fire guard

    CreateFireGuardResponse:
      type: object
      title: "CreateFireGuardResponse"
      properties:
        id:
          type: string
          description: The generated ID of the fire guard
        name:
          type: string
          description: Name of the fire guard
        createdAt:
          type: string
          format: date-time
          description: Timestamp when the fire guard was created

    UpdateFireGuardRequest:
      type: object
      title: "UpdateFireGuardRequest"
      properties:
        name:
          type: string
          description: Name of the fire guard
        email:
          type: string
          description: Email address of the fire guard
        phone:
          type: string
          description: Phone number of the fire guard
        employeeId:
          type: string
          description: Employee ID of the fire guard
        certificationNumber:
          type: string
          description: Certification number of the fire guard
        certificationExpiry:
          type: string
          format: date-time
          description: Expiration date of the fire guard's certification
        isActive:
          type: boolean
          description: Whether the fire guard is active
        notes:
          type: string
          description: Additional notes about the fire guard
        assignedBuildingIds:
          type: array
          items:
            type: string
          description: IDs of buildings assigned to the fire guard

    UpdateFireGuardResponse:
      type: object
      title: "UpdateFireGuardResponse"
      properties:
        id:
          type: string
          description: ID of the fire guard
        name:
          type: string
          description: Name of the fire guard
        updatedAt:
          type: string
          format: date-time
          description: Timestamp when the fire guard was updated

    FireGuardResponse:
      type: object
      title: "FireGuardResponse"
      properties:
        id:
          type: string
          description: ID of the fire guard
        name:
          type: string
          description: Name of the fire guard
        email:
          type: string
          description: Email address of the fire guard
        phone:
          type: string
          description: Phone number of the fire guard
        employeeId:
          type: string
          description: Employee ID of the fire guard
        certificationNumber:
          type: string
          description: Certification number of the fire guard
        certificationExpiry:
          type: string
          format: date-time
          description: Expiration date of the fire guard's certification
        isActive:
          type: boolean
          description: Whether the fire guard is active
        notes:
          type: string
          description: Additional notes about the fire guard
        assignedBuildingIds:
          type: array
          items:
            type: string
          description: IDs of buildings assigned to the fire guard
        currentBuildingId:
          type: string
          description: ID of the building the fire guard is currently assigned to
        lastActiveAt:
          type: string
          format: date-time
          description: Timestamp when the fire guard was last active
        lastCheckInTime:
          type: string
          format: date-time
          description: Timestamp of the fire guard's last check-in
        nextCheckInDue:
          type: string
          format: date-time
          description: Timestamp when the fire guard's next check-in is due
        lastLocation:
          type: object
          properties:
            latitude:
              type: number
              description: Latitude coordinate of the fire guard's last location
            longitude:
              type: number
              description: Longitude coordinate of the fire guard's last location
            timestamp:
              type: string
              format: date-time
              description: Timestamp of the fire guard's last location update
            locationName:
              type: string
              description: Human-readable name of the fire guard's last location
        createdAt:
          type: string
          format: date-time
          description: Timestamp when the fire guard was created
        updatedAt:
          type: string
          format: date-time
          description: Timestamp when the fire guard was last updated

    ListFireGuardsResponse:
      type: object
      title: "ListFireGuardsResponse"
      properties:
        items:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: ID of the fire guard
              name:
                type: string
                description: Name of the fire guard
              email:
                type: string
                description: Email address of the fire guard
              phone:
                type: string
                description: Phone number of the fire guard
              employeeId:
                type: string
                description: Employee ID of the fire guard
              isActive:
                type: boolean
                description: Whether the fire guard is active
              currentBuildingId:
                type: string
                description: ID of the building the fire guard is currently assigned to
              lastActiveAt:
                type: string
                format: date-time
                description: Timestamp when the fire guard was last active
              lastCheckInTime:
                type: string
                format: date-time
                description: Timestamp of the fire guard's last check-in
              lastLocation:
                type: object
                properties:
                  latitude:
                    type: number
                    description: Latitude coordinate of the fire guard's last location
                  longitude:
                    type: number
                    description: Longitude coordinate of the fire guard's last location
                  timestamp:
                    type: string
                    format: date-time
                    description: Timestamp of the fire guard's last location update
                  locationName:
                    type: string
                    description: Human-readable name of the fire guard's last location
              createdAt:
                type: string
                format: date-time
                description: Timestamp when the fire guard was created
        pagination:
          type: object
          properties:
            lastEvaluatedKey:
              type: string
              description: Pagination token for retrieving next page of results
            hasMore:
              type: boolean
              description: Whether there are more results available

    ListBuildingsResponse:
      type: object
      title: "ListBuildingsResponse"
      properties:
        items:
          type: array
          items:
            $ref: "#/components/schemas/Building"
        pagination:
          type: object
          properties:
            lastEvaluatedKey:
              type: string
              description: Pagination token for retrieving next page of results
            hasMore:
              type: boolean
              description: Whether there are more results available

    # Organization and Application schemas
    SubmitOrganizationApplicationRequest:
      type: object
      title: "SubmitOrganizationApplicationRequest"
      required:
        - organizationName
        - contactName
        - contactEmail
      properties:
        organizationName:
          type: string
          description: Name of the organization
        contactName:
          type: string
          description: Name of the contact person
        contactEmail:
          type: string
          description: Email of the contact person
        contactPhone:
          type: string
          description: Phone number of the contact person
        address:
          type: string
          description: Address of the organization
        city:
          type: string
          description: City of the organization
        state:
          type: string
          description: State of the organization
        zip:
          type: string
          description: ZIP code of the organization
        notes:
          type: string
          description: Additional notes about the organization
        website:
          type: string
          format: uri
          description: Website of the organization
        orgType:
          type: string
          enum:
            - "AUTHORITY"
            - "SERVICE_PROVIDER"
          default: "SERVICE_PROVIDER"
          description: Type of organization (default is SERVICE_PROVIDER)
        businessDetails:
          type: object
          description: Optional business details of the organization
          properties:
            taxId:
              type: string
              description: Tax ID of the organization
            registrationNumber:
              type: string
              description: Registration number of the organization
            foundedYear:
              type: integer
              description: Year the organization was founded
            employeeCount:
              type: integer
              description: Number of employees in the organization
            serviceAreas:
              type: array
              description: Geographic areas where the organization operates
              items:
                type: string
            jurisdictionType:
              type: string
              description: Type of jurisdiction for government entities
            governmentLevel:
              type: string
              description: Level of government for authority organizations
        servicesOffered:
          type: array
          description: Services offered by the organization
          items:
            type: string
        certifications:
          type: array
          description: Certifications held by the organization
          items:
            type: string
        additionalInformation:
          type: string
          description: Additional information about the organization

    SubmitOrganizationApplicationResponse:
      type: object
      title: "SubmitOrganizationApplicationResponse"
      properties:
        applicationId:
          type: string
          description: ID of the created application
        organizationName:
          type: string
          description: Name of the organization
        status:
          type: string
          description: Status of the application
        createdAt:
          type: string
          format: date-time
          description: Timestamp when the application was created

    ApproveApplicationRequest:
      type: object
      title: "ApproveApplicationRequest"
      required:
        - applicationId
        - approved
      properties:
        applicationId:
          type: string
          description: ID of the application to approve or reject.
        adminNotes:
          type: string
          description: Administrative notes regarding the approval or rejection.
        approved:
          type: boolean
          description: Set to true to approve the application, false to reject.
        rejectionReason:
          type: string
          description: Reason for rejection, required if 'approved' is false.

    ApproveApplicationResponse:
      type: object
      title: "ApproveApplicationResponse"
      properties:
        applicationId:
          type: string
          description: ID of the approved application
        organizationId:
          type: string
          description: ID of the created organization
        status:
          type: string
          description: Status of the application
        updatedAt:
          type: string
          format: date-time
          description: Timestamp when the application was approved

    CreateOrganizationRequest:
      type: object
      title: "CreateOrganizationRequest"
      required:
        - name
        - type
      properties:
        name:
          type: string
          description: Name of the organization
          example: "Acme Fire Protection Services"
        type:
          type: string
          enum: [AUTHORITY, SERVICE_PROVIDER, PROPERTY_MANAGEMENT]
          description: |
            Type of organization:
            * `AUTHORITY` - Government or regulatory fire authority
            * `SERVICE_PROVIDER` - Company providing fire safety services
            * `PROPERTY_MANAGEMENT` - Company managing properties with fire safety needs
          example: "SERVICE_PROVIDER"
        description:
          type: string
          description: Description of the organization
          example: "Professional fire protection equipment installation and maintenance services"
        # Legacy flat contact fields
        address:
          type: string
          description: Address of the organization
          example: "150 S 12th St, Phoenix, AZ 85034"
        contactEmail:
          type: string
          description: Email of the primary contact
          example: "<EMAIL>"
        contactPhone:
          type: string
          description: Phone number of the primary contact
          example: "+16025557890"
        # New nested contact info structure
        contactInfo:
          type: object
          description: Contact information for the organization
          properties:
            name:
              type: string
              description: Name of the primary contact
              example: "Jane Doe"
            email:
              type: string
              description: Email of the primary contact
              example: "<EMAIL>"
            phone:
              type: string
              description: Phone number of the primary contact
              example: "+16025557890"
            address:
              type: string
              description: Address of the primary contact
              example: "4025 N 24th St, Phoenix, AZ 85016"
        website:
          type: string
          description: Website of the organization
          example: "https://acmefireprotection.com"
        licenseNumber:
          type: string
          description: License number of the organization
          example: "FP-12345-AZ"
        logoUrl:
          type: string
          description: URL to the organization's logo
          example: "https://acmefireprotection.com/logo.png"
        additionalDetails:
          type: string
          description: Additional details about the organization
          example: "We provide professional fire protection equipment installation and maintenance services for commercial and residential properties."
      examples:
        authority:
          summary: Authority organization example
          value:
            name: "Phoenix Fire Department"
            type: "AUTHORITY"
            licenseNumber: "AUTH-7890-AZ"
            description: "Municipal fire authority responsible for fire safety enforcement"
            contactEmail: "<EMAIL>"
            contactPhone: "+16025551234"
            address: "150 S 12th St, Phoenix, AZ 85034"
            website: "https://phoenix.gov/fire"
        serviceProvider:
          summary: Service provider organization example
          value:
            type: "SERVICE_PROVIDER"
            name: "Acme Fire Protection Services"
            contactInfo:
              name: "Jane Doe"
              email: "<EMAIL>"
              phone: "+16025557890"
              address: "4025 N 24th St, Phoenix, AZ 85016"
            licenseNumber: "FP-12345-AZ"
            website: "https://acmefireprotection.com"
            additionalDetails: "We provide professional fire protection equipment installation and maintenance services for commercial and residential properties."

    CreateOrganizationResponse:
      type: object
      title: "CreateOrganizationResponse"
      properties:
        id:
          type: string
          description: ID of the created organization
          example: "org_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        clerkId:
          type: string
          description: Clerk ID of the organization
          example: "org_2QUu0EFyJPzV49iVsEn1gXDGypV"
        name:
          type: string
          description: Name of the organization
          example: "Acme Fire Protection Services"
        type:
          type: string
          description: Type of the organization
          example: "SERVICE_PROVIDER"
          enum: [AUTHORITY, SERVICE_PROVIDER, PROPERTY_MANAGEMENT]
        status:
          type: string
          description: Status of the organization
          example: "ACTIVE"
          enum: [ACTIVE, INACTIVE, PENDING, SUSPENDED]
        tenantId:
          type: string
          description: Tenant ID of the organization
          example: "tenant_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        createdAt:
          type: integer
          format: int64
          description: Timestamp when the organization was created (Unix timestamp in milliseconds)
          example: 1693526400000
        updatedAt:
          type: integer
          format: int64
          description: Timestamp when the organization was last updated (Unix timestamp in milliseconds)
          example: 1693526400000
      example:
        id: "org_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        clerkId: "org_2QUu0EFyJPzV49iVsEn1gXDGypV"
        name: "Acme Fire Protection Services"
        type: "SERVICE_PROVIDER"
        status: "ACTIVE"
        tenantId: "tenant_01H9XD5JNZQ3BYKP6ZCVGWTVMR"
        createdAt: 1693526400000
        updatedAt: 1693526400000



    # Webhook schemas
    ClerkWebhookRequest:
      type: object
      title: "ClerkWebhookRequest"
      properties:
        data:
          type: object
          description: The data of the webhook event
        object:
          type: string
          description: The type of object that triggered the event
        type:
          type: string
          description: The type of event
        created_at:
          type: string
          format: date-time
          description: Timestamp when the event was created

    # Standard response components
    ErrorResponse:
      type: object
      title: "ErrorResponse"
      properties:
        code:
          type: string
          description: Error code that can be used to programmatically identify the error
        message:
          type: string
          description: Human-readable error message
        details:
          type: object
          description: Additional details about the error, if available
          additionalProperties: true
      required:
        - code
        - message
      example:
        code: "VALIDATION_ERROR"
        message: "The request contains invalid parameters"
        details:
          field: "name"
          reason: "Name is required"

    ValidationError:
      type: object
      title: "ValidationError"
      properties:
        code:
          type: string
          enum: ["VALIDATION_ERROR"]
          description: Error code for validation errors
        message:
          type: string
          description: Human-readable error message
        validationErrors:
          type: array
          description: List of validation errors
          items:
            type: object
            properties:
              field:
                type: string
                description: The field that failed validation
              message:
                type: string
                description: The validation error message
              code:
                type: string
                description: Specific validation error code
      required:
        - code
        - message
        - validationErrors
      example:
        code: "VALIDATION_ERROR"
        message: "The request contains validation errors"
        validationErrors:
          - field: "name"
            message: "Name is required"
            code: "REQUIRED"
          - field: "email"
            message: "Invalid email format"
            code: "INVALID_FORMAT"

    AuthenticationError:
      type: object
      title: "AuthenticationError"
      properties:
        code:
          type: string
          enum: ["AUTHENTICATION_ERROR"]
          description: Error code for authentication errors
        message:
          type: string
          description: Human-readable error message
      required:
        - code
        - message
      example:
        code: "AUTHENTICATION_ERROR"
        message: "Authentication credentials are missing or invalid"

    AuthorizationError:
      type: object
      title: "AuthorizationError"
      properties:
        code:
          type: string
          enum: ["AUTHORIZATION_ERROR"]
          description: Error code for authorization errors
        message:
          type: string
          description: Human-readable error message
        requiredPermissions:
          type: array
          description: List of permissions required to access the resource
          items:
            type: string
      required:
        - code
        - message
      example:
        code: "AUTHORIZATION_ERROR"
        message: "You do not have permission to access this resource"
        requiredPermissions: ["ADMIN", "BUILDING_MANAGER"]

    ResourceNotFoundError:
      type: object
      title: "ResourceNotFoundError"
      properties:
        code:
          type: string
          enum: ["RESOURCE_NOT_FOUND"]
          description: Error code for resource not found errors
        message:
          type: string
          description: Human-readable error message
        resourceType:
          type: string
          description: Type of resource that was not found
        resourceId:
          type: string
          description: ID of the resource that was not found
      required:
        - code
        - message
      example:
        code: "RESOURCE_NOT_FOUND"
        message: "The requested resource was not found"
        resourceType: "Building"
        resourceId: "bld_123456789"

    PaginationMetadata:
      type: object
      title: "PaginationMetadata"
      properties:
        totalCount:
          type: integer
          description: Total number of items available
        pageSize:
          type: integer
          description: Number of items per page
        currentPage:
          type: integer
          description: Current page number
        totalPages:
          type: integer
          description: Total number of pages
        hasNextPage:
          type: boolean
          description: Whether there are more pages available
        hasPreviousPage:
          type: boolean
          description: Whether there are previous pages available
        nextPageToken:
          type: string
          description: Token to use for fetching the next page
        previousPageToken:
          type: string
          description: Token to use for fetching the previous page
      example:
        totalCount: 100
        pageSize: 10
        currentPage: 2
        totalPages: 10
        hasNextPage: true
        hasPreviousPage: true
        nextPageToken: "eyJsYXN0SWQiOiIxMjM0NTY3ODkwIiwibGltaXQiOjEwfQ=="
        previousPageToken: "eyJsYXN0SWQiOiI5ODc2NTQzMjEiLCJsaW1pdCI6MTB9"

paths:
  /buildings:
    post:
      tags:
        - Buildings
      operationId: createBuilding
      summary: Create a new building
      description: |
        Creates a new building or facility in the FireGuard system.

        Buildings represent physical structures that require fire safety monitoring. Each building can have multiple fire guards assigned to it.

        Required fields include name, address, and the organization ID that owns the building.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateBuildingRequest"
      responses:
        "201":
          description: "Building created successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BuildingResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: "Unauthorized - authentication required"
        "403":
          description: "Forbidden - insufficient permissions"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${CreateBuildingFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"
    get:
      tags:
        - Buildings
      operationId: listBuildings
      summary: List buildings
      description: |
        Retrieves a paginated list of buildings for the authenticated organization.

        Results can be filtered and paginated using the provided query parameters.
        The buildings are returned sorted by creation date, with the newest buildings appearing first.
      parameters:
        - $ref: "#/components/parameters/PageSize"
        - $ref: "#/components/parameters/PageToken"
        - $ref: "#/components/parameters/SortBy"
        - $ref: "#/components/parameters/SortOrder"
      responses:
        "200":
          description: "Buildings retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListBuildingsResponse"
        "401":
          description: "Unauthorized - authentication required"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ListBuildingsFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /buildings/{buildingId}:
    parameters:
      - name: buildingId
        in: path
        required: true
        schema:
          type: string
        description: The unique identifier of the building to retrieve
    get:
      tags:
        - Buildings
      operationId: getBuilding
      summary: Get building details
      description: |
        Retrieves detailed information about a specific building.

        This endpoint returns comprehensive information about the building, including its physical attributes,
        fire protection systems, and risk assessment data. The building must belong to the authenticated organization.
      responses:
        "200":
          description: "Building retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Building"
        "404":
          description: "Building not found"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GetBuildingFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /guards:
    get:
      tags:
        - Guards
      operationId: listGuards
      summary: List fire guards
      description: |
        Retrieves a paginated list of fire guards for the authenticated organization.

        Results can be filtered by building assignment and active status.
        The guards are returned sorted by creation date, with the newest guards appearing first.

        This endpoint supports pagination to handle large result sets efficiently.
      parameters:
        - $ref: "#/components/parameters/PageSize"
        - $ref: "#/components/parameters/PageToken"
        - $ref: "#/components/parameters/SortBy"
        - $ref: "#/components/parameters/SortOrder"
        - name: buildingId
          in: query
          required: false
          schema:
            type: string
          description: Filter guards by assigned building ID
        - name: isActive
          in: query
          required: false
          schema:
            type: boolean
          description: Filter guards by active status
      responses:
        "200":
          description: "Fire guards retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListFireGuardsResponse"
        "401":
          description: "Unauthorized - authentication required"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ListGuardsFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"
    post:
      tags:
        - Guards
      operationId: createGuard
      summary: Create a new fire guard
      description: |
        Creates a new fire guard in the FireGuard system.

        Fire guards are personnel responsible for monitoring and ensuring fire safety in buildings.
        Each guard can be assigned to specific buildings and must maintain certifications.

        The guard will be created under the authenticated organization.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateFireGuardRequest"
      responses:
        "201":
          description: "Fire guard created successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateFireGuardResponse"
        "400":
          description: "Bad request - validation error"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: "Unauthorized - authentication required"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${CreateGuardFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /guards/{guardId}:
    parameters:
      - name: guardId
        in: path
        required: true
        schema:
          type: string
        description: ID of the fire guard
    get:
      summary: Get fire guard details
      description: Retrieve details for a specific fire guard
      parameters:
        - name: includeLatestLocation
          in: query
          required: false
          schema:
            type: boolean
            default: false
          description: Whether to include the latest location information
      responses:
        "200":
          description: "Fire guard retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FireGuardResponse"
        "404":
          description: "Fire guard not found"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GetGuardFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"
    put:
      summary: Update fire guard details
      description: Update details for a specific fire guard
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateFireGuardRequest"
      responses:
        "200":
          description: "Fire guard updated successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateFireGuardResponse"
        "400":
          description: "Bad request - validation error"
        "404":
          description: "Fire guard not found"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${UpdateGuardFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /guards/location:
    post:
      summary: Update a guard's location
      description: Update a guard's location and create a location history entry
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateGuardLocationRequest"
      responses:
        "200":
          description: "Guard location updated successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateGuardLocationResponse"
        "400":
          description: "Bad request - validation error"
        "401":
          description: "Unauthorized - authentication required"
        "404":
          description: "Guard not found"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${UpdateGuardLocationFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /guards/{guardId}/location-history:
    parameters:
      - name: guardId
        in: path
        required: true
        schema:
          type: string
        description: ID of the guard
    get:
      summary: Get guard location history
      description: Retrieve location history for a specific guard
      parameters:
        - name: startTime
          in: query
          required: false
          schema:
            type: string
            format: date-time
          description: Start time for filtering location history
        - name: endTime
          in: query
          required: false
          schema:
            type: string
            format: date-time
          description: End time for filtering location history
        - $ref: "#/components/parameters/PageSize"
        - $ref: "#/components/parameters/PageToken"
        - $ref: "#/components/parameters/SortBy"
        - $ref: "#/components/parameters/SortOrder"
        - name: latestOnly
          in: query
          required: false
          schema:
            type: boolean
            default: false
          description: If true, returns only the latest location
      responses:
        "200":
          description: "Guard location history retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LocationHistoryResponse"
        "404":
          description: "Guard not found or no location history available"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GetGuardLocationHistoryFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /guards/outside-geofence:
    get:
      summary: Find guards outside geofence
      description: Find guards who are currently outside their assigned geofence
      responses:
        "200":
          description: "Guards outside geofence retrieved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GuardsOutsideGeofenceResponse"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${FindGuardsOutsideGeofenceFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  # Organization and Application endpoints
  /applications/organizations:
    post:
      tags:
        - Applications
        - Organizations
      operationId: submitOrganizationApplication
      summary: Submit organization application
      description: |
        Submits a new organization application for review.

        Organizations must go through an application process before being approved in the system.
        This endpoint allows prospective organizations to submit their information for review by system administrators.

        Once submitted, the application will be reviewed and can be approved or rejected.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SubmitOrganizationApplicationRequest"
      responses:
        "201":
          description: "Application submitted successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubmitOrganizationApplicationResponse"
        "400":
          description: "Bad request - validation error"
        "401":
          description: "Unauthorized - authentication required"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${SubmitOrganizationApplicationFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /applications/organizations/{applicationId}/approve:
    post:
      tags:
        - Applications
        - Organizations
      operationId: approveOrganizationApplication
      summary: Approve organization application
      description: |
        Approves or rejects a pending organization application.

        This endpoint is restricted to system administrators. When an application is approved,
        a new organization is created in the system based on the application details.

        The applicant will be notified of the decision.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ApproveApplicationRequest"
      responses:
        "200":
          description: "Application approved successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ApproveApplicationResponse"
        "400":
          description: "Bad request - validation error"
        "401":
          description: "Unauthorized - authentication required"
        "404":
          description: "Application not found"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ApproveApplicationFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

  /organizations:
    post:
      tags:
        - Organizations
      operationId: createOrganization
      summary: Create organization
      description: |
        Creates a new organization in the FireGuard system.

        Organizations are entities that manage buildings and fire guards. They can be authorities (like fire departments),
        service providers (companies that provide fire safety services), or property management companies.

        This endpoint is typically used by system administrators or through the application approval process.
        Each organization has its own isolated data and users.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrganizationRequest"
      responses:
        "201":
          description: "Organization created successfully"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateOrganizationResponse"
        "400":
          description: "Bad request - validation error"
        "401":
          description: "Unauthorized - authentication required"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${CreateOrganizationFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"



  # Webhook endpoints
  /webhooks/clerk:
    post:
      summary: Clerk webhook handler
      description: Handles webhook events from Clerk
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ClerkWebhookRequest"
      responses:
        "200":
          description: "Webhook processed successfully"
        "400":
          description: "Bad request - validation error"
        "401":
          description: "Unauthorized - invalid signature"
        "500":
          description: "Internal server error"
      x-amazon-apigateway-integration:
        type: "aws_proxy"
        httpMethod: "POST"
        uri:
          {
            "Fn::Sub": "arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${ClerkWebhookFunctionArn}/invocations",
          }
        passthroughBehavior: "when_no_match"

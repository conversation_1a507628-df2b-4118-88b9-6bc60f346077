{"name": "clerk-webhooks", "version": "1.0.0", "description": "Lambda functions for handling Clerk webhook events", "main": "index.js", "scripts": {"test": "jest", "build": "tsc"}, "dependencies": {"@aws-lambda-powertools/logger": "^1.11.0", "@aws-lambda-powertools/metrics": "^1.11.0", "@aws-lambda-powertools/tracer": "^1.11.0", "@aws-sdk/client-dynamodb": "^3.350.0", "@aws-sdk/client-sns": "^3.350.0", "@aws-sdk/lib-dynamodb": "^3.350.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/jest": "^29.5.3", "@types/node": "^18.16.0", "esbuild": "^0.17.19", "jest": "^29.6.1", "ts-jest": "^29.1.1", "typescript": "^5.1.3"}}
{"name": "hello_world", "version": "1.0.0", "description": "hello world sample for NodeJS", "main": "app.js", "repository": "https://github.com/awslabs/aws-sam-cli/tree/develop/samcli/local/init/templates/cookiecutter-aws-sam-hello-nodejs", "author": "SAM CLI", "license": "MIT", "scripts": {"unit": "jest", "lint": "eslint '*.ts' --quiet --fix", "compile": "tsc", "test": "npm run compile && npm run unit"}, "dependencies": {"esbuild": "^0.14.14"}, "devDependencies": {"@types/aws-lambda": "^8.10.92", "@types/jest": "^29.2.0", "@jest/globals": "^29.2.0", "@types/node": "^20.5.7", "@typescript-eslint/eslint-plugin": "^5.10.2", "@typescript-eslint/parser": "^5.10.2", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.2.1", "prettier": "^2.5.1", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^4.8.4"}}